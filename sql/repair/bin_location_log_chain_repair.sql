-- BinLocationLog链路修复脚本
-- 用于修复BinLocationLog表中的断链问题
-- 作者: felix
-- 日期: 2025-01-07

-- 创建临时表用于存储修复数据
DROP TABLE IF EXISTS temp_bin_location_log_repair;

CREATE TABLE temp_bin_location_log_repair (
    log_id BIGINT NOT NULL,
    product_version_id BIGINT NOT NULL,
    bin_location_detail_id BIGINT NOT NULL,
    qty_side VARCHAR(10) NOT NULL,
    old_before_qty INT,
    old_after_qty INT,
    old_change_qty INT,
    new_before_qty INT,
    new_after_qty INT,
    new_change_qty INT,
    change_type VARCHAR(255),
    create_time TIMESTAMP,
    repair_order INT,
    INDEX idx_log_id (log_id),
    INDEX idx_product_location (product_version_id, bin_location_detail_id)
);

-- 1. 检测断链记录并插入临时表
INSERT INTO temp_bin_location_log_repair (
    log_id, product_version_id, bin_location_detail_id, qty_side,
    old_before_qty, old_after_qty, old_change_qty, change_type, create_time, repair_order
)
WITH qty_union AS (
    /* ---------- source 侧 ---------- */
    SELECT
        l.id   AS log_id,
        l.product_version_id,
        l.product_id,
        l.source_bin_location_detail_id AS bin_location_detail_id,
        l.source_bin_location_id        AS bin_location_id,
        l.create_time,
        'source'                        AS qty_side,
        l.source_before_in_stock_qty    AS before_qty,
        l.source_change_in_stock_qty    AS change_qty,
        l.source_after_in_stock_qty     AS after_qty,
        l.change_type
    FROM   frp_log_dev.bin_location_log l
    WHERE l.remove_flag = 0
    UNION ALL
    /* ---------- dest 侧（若与 source 完全重复则排除） ---------- */
    SELECT
        l.id,
        l.product_version_id,
        l.product_id,
        l.dest_bin_location_detail_id,
        l.dest_bin_location_id,
        l.create_time,
        'dest',
        l.dest_before_in_stock_qty,
        l.dest_change_in_stock_qty,
        l.dest_after_in_stock_qty,
        l.change_type
    FROM   frp_log_dev.bin_location_log l
    WHERE  NOT (
               l.dest_bin_location_detail_id = l.source_bin_location_detail_id
           AND l.dest_change_in_stock_qty    = l.source_change_in_stock_qty
           )
           AND l.remove_flag = 0
)
/* ===== 2. 用窗口函数做链路校验 ===== */
, qty_chain AS (
    SELECT
        q.*,
        LAG(q.after_qty) OVER (
            PARTITION BY q.product_version_id, q.bin_location_detail_id
            ORDER BY     q.create_time, q.log_id, q.qty_side
        ) AS prev_after_qty,
        ROW_NUMBER() OVER (
            PARTITION BY q.product_version_id, q.bin_location_detail_id
            ORDER BY     q.create_time, q.log_id, q.qty_side
        ) AS repair_order
    FROM qty_union q
)
/* ===== 3. 选择断链记录 ===== */
SELECT
    qc.log_id,
    qc.product_version_id,
    qc.bin_location_detail_id,
    qc.qty_side,
    qc.before_qty,
    qc.after_qty,
    qc.change_qty,
    qc.change_type,
    qc.create_time,
    qc.repair_order
FROM   qty_chain qc
WHERE  qc.prev_after_qty IS NOT NULL        -- 排除组首行
  AND  qc.prev_after_qty <> qc.before_qty    -- 断档判定
ORDER BY
    qc.product_version_id,
    qc.bin_location_detail_id,
    qc.create_time,
    qc.log_id,
    qc.qty_side;

-- 2. 计算修复后的正确数值
-- 2.1 首先设置初始的new_before_qty为原始值
UPDATE temp_bin_location_log_repair t1
INNER JOIN frp_log_dev.bin_location_log l ON t1.log_id = l.id
SET t1.new_before_qty = CASE
    WHEN t1.qty_side = 'source' THEN l.source_before_in_stock_qty
    ELSE l.dest_before_in_stock_qty
END;

-- 2.2 创建临时表存储修复计算结果
DROP TABLE IF EXISTS temp_repair_calc;
CREATE TABLE temp_repair_calc AS
SELECT
    t1.log_id,
    t1.product_version_id,
    t1.bin_location_detail_id,
    t1.qty_side,
    t1.repair_order,
    t1.old_before_qty,
    t1.old_after_qty,
    t1.old_change_qty,
    t1.change_type,
    COALESCE(t2.new_after_qty, t1.old_before_qty) AS calculated_before_qty
FROM temp_bin_location_log_repair t1
LEFT JOIN temp_bin_location_log_repair t2 ON (
    t2.product_version_id = t1.product_version_id
    AND t2.bin_location_detail_id = t1.bin_location_detail_id
    AND t2.repair_order = t1.repair_order - 1
);

-- 2.3 更新new_before_qty
UPDATE temp_bin_location_log_repair t1
INNER JOIN temp_repair_calc tc ON t1.log_id = tc.log_id
SET t1.new_before_qty = tc.calculated_before_qty;

-- 3. 计算修复后的after_qty和change_qty
UPDATE temp_bin_location_log_repair
SET 
    new_after_qty = CASE 
        WHEN change_type = 'InventoryAudit' THEN new_before_qty
        ELSE new_before_qty + old_change_qty
    END,
    new_change_qty = CASE 
        WHEN change_type = 'InventoryAudit' THEN 0
        ELSE old_change_qty
    END;

-- 4. 显示修复预览
SELECT 
    '修复预览' AS action,
    log_id,
    product_version_id,
    bin_location_detail_id,
    qty_side,
    change_type,
    CONCAT(old_before_qty, ' -> ', new_before_qty) AS before_qty_change,
    CONCAT(old_after_qty, ' -> ', new_after_qty) AS after_qty_change,
    CASE 
        WHEN old_change_qty != new_change_qty THEN CONCAT(old_change_qty, ' -> ', new_change_qty)
        ELSE CAST(old_change_qty AS CHAR)
    END AS change_qty_info
FROM temp_bin_location_log_repair
ORDER BY product_version_id, bin_location_detail_id, repair_order;

-- 5. 执行修复（取消注释以下代码来执行实际修复）
/*
-- 修复source侧数据
UPDATE frp_log_dev.bin_location_log l
INNER JOIN temp_bin_location_log_repair t ON l.id = t.log_id AND t.qty_side = 'source'
SET 
    l.source_before_in_stock_qty = t.new_before_qty,
    l.source_after_in_stock_qty = t.new_after_qty,
    l.source_change_in_stock_qty = t.new_change_qty;

-- 修复dest侧数据
UPDATE frp_log_dev.bin_location_log l
INNER JOIN temp_bin_location_log_repair t ON l.id = t.log_id AND t.qty_side = 'dest'
SET 
    l.dest_before_in_stock_qty = t.new_before_qty,
    l.dest_after_in_stock_qty = t.new_after_qty,
    l.dest_change_in_stock_qty = t.new_change_qty;
*/

-- 6. 修复统计
SELECT 
    '修复统计' AS info,
    COUNT(*) AS total_records_to_repair,
    COUNT(DISTINCT product_version_id) AS affected_products,
    COUNT(DISTINCT bin_location_detail_id) AS affected_locations,
    COUNT(DISTINCT CASE WHEN qty_side = 'source' THEN log_id END) AS source_records,
    COUNT(DISTINCT CASE WHEN qty_side = 'dest' THEN log_id END) AS dest_records,
    COUNT(DISTINCT CASE WHEN change_type = 'InventoryAudit' THEN log_id END) AS inventory_audit_records
FROM temp_bin_location_log_repair;

-- 7. 按产品统计
SELECT 
    '按产品统计' AS info,
    product_version_id,
    COUNT(*) AS broken_records,
    COUNT(DISTINCT bin_location_detail_id) AS affected_locations
FROM temp_bin_location_log_repair
GROUP BY product_version_id
ORDER BY broken_records DESC;

-- 8. 按库位统计
SELECT 
    '按库位统计' AS info,
    bin_location_detail_id,
    COUNT(*) AS broken_records,
    COUNT(DISTINCT product_version_id) AS affected_products
FROM temp_bin_location_log_repair
GROUP BY bin_location_detail_id
ORDER BY broken_records DESC;

-- 清理临时表
DROP TABLE IF EXISTS temp_repair_calc;
-- DROP TABLE IF EXISTS temp_bin_location_log_repair; -- 如果不需要查看结果可以取消注释

-- 注意：
-- 1. 此脚本首先会显示修复预览，不会立即执行修复
-- 2. 要执行实际修复，请取消注释第5步中的UPDATE语句
-- 3. 建议在执行修复前备份相关数据
-- 4. 修复完成后可以删除临时表：DROP TABLE temp_bin_location_log_repair;
