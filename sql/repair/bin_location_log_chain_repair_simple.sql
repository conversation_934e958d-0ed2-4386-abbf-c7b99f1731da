-- BinLocationLog链路修复脚本 - 简化版本
-- 用于修复BinLocationLog表中的断链问题
-- 作者: felix
-- 日期: 2025-01-07

-- 1. 创建断链检测结果表
DROP TABLE IF EXISTS broken_chains_result;

CREATE TABLE broken_chains_result (
    log_id BIGINT NOT NULL,
    product_version_id BIGINT NOT NULL,
    bin_location_detail_id BIGINT NOT NULL,
    qty_side VARCHAR(10) NOT NULL,
    before_qty INT,
    change_qty INT,
    after_qty INT,
    prev_after_qty INT,
    change_type VARCHAR(255),
    create_time TIMESTAMP,
    chain_order INT,
    PRIMARY KEY (log_id, qty_side),
    INDEX idx_product_location (product_version_id, bin_location_detail_id)
);

-- 2. 检测断链记录
INSERT INTO broken_chains_result (
    log_id, product_version_id, bin_location_detail_id, qty_side,
    before_qty, change_qty, after_qty, prev_after_qty, change_type, create_time, chain_order
)
WITH qty_union AS (
    /* ---------- source 侧 ---------- */
    SELECT
        l.id   AS log_id,
        l.product_version_id,
        l.source_bin_location_detail_id AS bin_location_detail_id,
        l.create_time,
        'source'                        AS qty_side,
        l.source_before_in_stock_qty    AS before_qty,
        l.source_change_in_stock_qty    AS change_qty,
        l.source_after_in_stock_qty     AS after_qty,
        l.change_type
    FROM   frp_log_dev.bin_location_log l
    WHERE l.remove_flag = 0
    UNION ALL
    /* ---------- dest 侧（若与 source 完全重复则排除） ---------- */
    SELECT
        l.id,
        l.product_version_id,
        l.dest_bin_location_detail_id,
        l.create_time,
        'dest',
        l.dest_before_in_stock_qty,
        l.dest_change_in_stock_qty,
        l.dest_after_in_stock_qty,
        l.change_type
    FROM   frp_log_dev.bin_location_log l
    WHERE  NOT (
               l.dest_bin_location_detail_id = l.source_bin_location_detail_id
           AND l.dest_change_in_stock_qty    = l.source_change_in_stock_qty
           )
           AND l.remove_flag = 0
)
/* ===== 2. 用窗口函数做链路校验 ===== */
, qty_chain AS (
    SELECT
        q.*,
        LAG(q.after_qty) OVER (
            PARTITION BY q.product_version_id, q.bin_location_detail_id
            ORDER BY     q.create_time, q.log_id, q.qty_side
        ) AS prev_after_qty,
        ROW_NUMBER() OVER (
            PARTITION BY q.product_version_id, q.bin_location_detail_id
            ORDER BY     q.create_time, q.log_id, q.qty_side
        ) AS chain_order
    FROM qty_union q
)
/* ===== 3. 选择断链记录 ===== */
SELECT
    qc.log_id,
    qc.product_version_id,
    qc.bin_location_detail_id,
    qc.qty_side,
    qc.before_qty,
    qc.change_qty,
    qc.after_qty,
    qc.prev_after_qty,
    qc.change_type,
    qc.create_time,
    qc.chain_order
FROM   qty_chain qc
WHERE  qc.prev_after_qty IS NOT NULL        -- 排除组首行
  AND  qc.prev_after_qty <> qc.before_qty    -- 断档判定
ORDER BY
    qc.product_version_id,
    qc.bin_location_detail_id,
    qc.create_time,
    qc.log_id,
    qc.qty_side;

-- 3. 显示检测结果统计
SELECT 
    '断链检测统计' AS info,
    COUNT(*) AS total_broken_records,
    COUNT(DISTINCT product_version_id) AS affected_products,
    COUNT(DISTINCT bin_location_detail_id) AS affected_locations,
    COUNT(DISTINCT CASE WHEN qty_side = 'source' THEN log_id END) AS source_records,
    COUNT(DISTINCT CASE WHEN qty_side = 'dest' THEN log_id END) AS dest_records,
    COUNT(DISTINCT CASE WHEN change_type = 'InventoryAudit' THEN log_id END) AS inventory_audit_records
FROM broken_chains_result;

-- 4. 显示前20条断链记录
SELECT 
    '断链记录预览' AS info,
    log_id,
    product_version_id,
    bin_location_detail_id,
    qty_side,
    change_type,
    CONCAT(prev_after_qty, ' -> ', before_qty) AS qty_break,
    before_qty,
    change_qty,
    after_qty,
    create_time
FROM broken_chains_result
ORDER BY product_version_id, bin_location_detail_id, chain_order
LIMIT 20;

-- 5. 创建完整链路数据表（从断链点到InventoryAudit）
DROP TABLE IF EXISTS full_chain_data;

CREATE TABLE full_chain_data (
    log_id BIGINT NOT NULL,
    qty_side VARCHAR(10) NOT NULL,
    product_version_id BIGINT NOT NULL,
    bin_location_detail_id BIGINT NOT NULL,
    before_qty INT,
    change_qty INT,
    after_qty INT,
    change_type VARCHAR(255),
    create_time TIMESTAMP,
    chain_order INT,
    is_break_point TINYINT DEFAULT 0,
    is_inventory_audit TINYINT DEFAULT 0,
    PRIMARY KEY (log_id, qty_side),
    INDEX idx_product_location_order (product_version_id, bin_location_detail_id, chain_order)
);

-- 6. 获取完整的链路数据（从断链点到InventoryAudit）
INSERT INTO full_chain_data (
    log_id, qty_side, product_version_id, bin_location_detail_id,
    before_qty, change_qty, after_qty, change_type, create_time, chain_order,
    is_break_point, is_inventory_audit
)
WITH all_chain_data AS (
    /* ---------- 获取所有链路数据 ---------- */
    SELECT
        l.id   AS log_id,
        l.product_version_id,
        l.source_bin_location_detail_id AS bin_location_detail_id,
        l.create_time,
        'source'                        AS qty_side,
        l.source_before_in_stock_qty    AS before_qty,
        l.source_change_in_stock_qty    AS change_qty,
        l.source_after_in_stock_qty     AS after_qty,
        l.change_type,
        ROW_NUMBER() OVER (
            PARTITION BY l.product_version_id, l.source_bin_location_detail_id
            ORDER BY l.create_time, l.id, 'source'
        ) AS chain_order
    FROM   frp_log_dev.bin_location_log l
    WHERE l.remove_flag = 0
    UNION ALL
    SELECT
        l.id,
        l.product_version_id,
        l.dest_bin_location_detail_id,
        l.create_time,
        'dest',
        l.dest_before_in_stock_qty,
        l.dest_change_in_stock_qty,
        l.dest_after_in_stock_qty,
        l.change_type,
        ROW_NUMBER() OVER (
            PARTITION BY l.product_version_id, l.dest_bin_location_detail_id
            ORDER BY l.create_time, l.id, 'dest'
        ) AS chain_order
    FROM   frp_log_dev.bin_location_log l
    WHERE  NOT (
               l.dest_bin_location_detail_id = l.source_bin_location_detail_id
           AND l.dest_change_in_stock_qty    = l.source_change_in_stock_qty
           )
           AND l.remove_flag = 0
),
break_points AS (
    /* ---------- 获取断链点信息 ---------- */
    SELECT DISTINCT
        product_version_id,
        bin_location_detail_id,
        MIN(chain_order) as min_break_order
    FROM broken_chains_result
    GROUP BY product_version_id, bin_location_detail_id
),
repair_ranges AS (
    /* ---------- 确定修复范围：从断链点到下一个InventoryAudit ---------- */
    SELECT
        acd.*,
        CASE WHEN bp.product_version_id IS NOT NULL THEN 1 ELSE 0 END as is_break_point,
        CASE WHEN acd.change_type = 'InventoryAudit' THEN 1 ELSE 0 END as is_inventory_audit,
        bp.min_break_order
    FROM all_chain_data acd
    LEFT JOIN break_points bp ON (
        acd.product_version_id = bp.product_version_id
        AND acd.bin_location_detail_id = bp.bin_location_detail_id
        AND acd.chain_order >= bp.min_break_order
    )
    WHERE bp.product_version_id IS NOT NULL  -- 只处理有断链的产品和库位
)
SELECT
    rr.log_id,
    rr.qty_side,
    rr.product_version_id,
    rr.bin_location_detail_id,
    rr.before_qty,
    rr.change_qty,
    rr.after_qty,
    rr.change_type,
    rr.create_time,
    rr.chain_order,
    rr.is_break_point,
    rr.is_inventory_audit
FROM repair_ranges rr
WHERE rr.chain_order >= rr.min_break_order
  AND rr.chain_order <= COALESCE(
      (SELECT MIN(rr2.chain_order)
       FROM repair_ranges rr2
       WHERE rr2.product_version_id = rr.product_version_id
         AND rr2.bin_location_detail_id = rr.bin_location_detail_id
         AND rr2.chain_order > rr.min_break_order
         AND rr2.is_inventory_audit = 1),
      999999  -- 如果没有InventoryAudit，修复到最后
  )
ORDER BY rr.product_version_id, rr.bin_location_detail_id, rr.chain_order;

-- 7. 创建修复计算表
DROP TABLE IF EXISTS repair_calculations;

CREATE TABLE repair_calculations (
    log_id BIGINT NOT NULL,
    qty_side VARCHAR(10) NOT NULL,
    product_version_id BIGINT NOT NULL,
    bin_location_detail_id BIGINT NOT NULL,
    old_before_qty INT,
    old_after_qty INT,
    old_change_qty INT,
    new_before_qty INT,
    new_after_qty INT,
    new_change_qty INT,
    change_type VARCHAR(255),
    chain_order INT,
    is_break_point TINYINT DEFAULT 0,
    is_inventory_audit TINYINT DEFAULT 0,
    PRIMARY KEY (log_id, qty_side)
);

-- 8. 插入需要修复的数据
INSERT INTO repair_calculations (
    log_id, qty_side, product_version_id, bin_location_detail_id,
    old_before_qty, old_after_qty, old_change_qty, change_type, chain_order,
    is_break_point, is_inventory_audit
)
SELECT
    log_id, qty_side, product_version_id, bin_location_detail_id,
    before_qty, after_qty, change_qty, change_type, chain_order,
    is_break_point, is_inventory_audit
FROM full_chain_data;

-- 9. 逐步修复链路数据
-- 9.1 设置断链点的正确before_qty（使用断链检测结果中的prev_after_qty）
UPDATE repair_calculations rc
INNER JOIN broken_chains_result bcr ON (
    rc.log_id = bcr.log_id
    AND rc.qty_side = bcr.qty_side
    AND rc.is_break_point = 1
)
SET rc.new_before_qty = bcr.prev_after_qty;

-- 9.2 对于非断链点的第一条记录，保持原始before_qty
UPDATE repair_calculations rc
SET rc.new_before_qty = rc.old_before_qty
WHERE rc.is_break_point = 0
  AND rc.chain_order = (
      SELECT MIN(rc2.chain_order)
      FROM repair_calculations rc2
      WHERE rc2.product_version_id = rc.product_version_id
        AND rc2.bin_location_detail_id = rc.bin_location_detail_id
  );

-- 9.3 创建临时表用于迭代计算
DROP TABLE IF EXISTS temp_iteration;
CREATE TABLE temp_iteration AS
SELECT
    log_id, qty_side, product_version_id, bin_location_detail_id,
    old_before_qty, old_after_qty, old_change_qty,
    new_before_qty, change_type, chain_order,
    is_break_point, is_inventory_audit
FROM repair_calculations
ORDER BY product_version_id, bin_location_detail_id, chain_order;

-- 9.4 迭代修复链路（需要多次执行直到收敛）
-- 第1次迭代
UPDATE repair_calculations rc1
INNER JOIN temp_iteration ti ON (
    rc1.product_version_id = ti.product_version_id
    AND rc1.bin_location_detail_id = ti.bin_location_detail_id
    AND rc1.chain_order = ti.chain_order + 1
)
SET rc1.new_before_qty = CASE
    WHEN ti.change_type = 'InventoryAudit' THEN ti.new_before_qty  -- InventoryAudit后，before_qty = after_qty
    ELSE COALESCE(ti.new_before_qty + ti.old_change_qty, rc1.old_before_qty)
END
WHERE rc1.new_before_qty IS NULL;

-- 更新临时表
UPDATE temp_iteration ti
INNER JOIN repair_calculations rc ON (
    ti.log_id = rc.log_id AND ti.qty_side = rc.qty_side
)
SET ti.new_before_qty = rc.new_before_qty;

-- 第2次迭代（处理更长的链路）
UPDATE repair_calculations rc1
INNER JOIN temp_iteration ti ON (
    rc1.product_version_id = ti.product_version_id
    AND rc1.bin_location_detail_id = ti.bin_location_detail_id
    AND rc1.chain_order = ti.chain_order + 1
)
SET rc1.new_before_qty = CASE
    WHEN ti.change_type = 'InventoryAudit' THEN ti.new_before_qty
    ELSE COALESCE(ti.new_before_qty + ti.old_change_qty, rc1.old_before_qty)
END
WHERE rc1.new_before_qty IS NULL;

-- 更新临时表
UPDATE temp_iteration ti
INNER JOIN repair_calculations rc ON (
    ti.log_id = rc.log_id AND ti.qty_side = rc.qty_side
)
SET ti.new_before_qty = rc.new_before_qty;

-- 第3次迭代（处理更长的链路）
UPDATE repair_calculations rc1
INNER JOIN temp_iteration ti ON (
    rc1.product_version_id = ti.product_version_id
    AND rc1.bin_location_detail_id = ti.bin_location_detail_id
    AND rc1.chain_order = ti.chain_order + 1
)
SET rc1.new_before_qty = CASE
    WHEN ti.change_type = 'InventoryAudit' THEN ti.new_before_qty
    ELSE COALESCE(ti.new_before_qty + ti.old_change_qty, rc1.old_before_qty)
END
WHERE rc1.new_before_qty IS NULL;

-- 9.5 对于仍然为NULL的记录，使用原始值
UPDATE repair_calculations
SET new_before_qty = old_before_qty
WHERE new_before_qty IS NULL;

-- 9.6 计算new_after_qty和new_change_qty
UPDATE repair_calculations
SET
    new_after_qty = CASE
        WHEN change_type = 'InventoryAudit' THEN new_before_qty  -- InventoryAudit: after_qty = before_qty
        ELSE new_before_qty + old_change_qty
    END,
    new_change_qty = CASE
        WHEN change_type = 'InventoryAudit' THEN 0  -- InventoryAudit: change_qty = 0
        ELSE old_change_qty
    END;

-- 10. 显示修复范围统计
SELECT
    '修复范围统计' AS info,
    COUNT(*) AS total_records_in_repair_range,
    COUNT(DISTINCT product_version_id) AS affected_products,
    COUNT(DISTINCT bin_location_detail_id) AS affected_locations,
    COUNT(DISTINCT CASE WHEN qty_side = 'source' THEN log_id END) AS source_records,
    COUNT(DISTINCT CASE WHEN qty_side = 'dest' THEN log_id END) AS dest_records,
    SUM(is_break_point) AS break_point_records,
    SUM(is_inventory_audit) AS inventory_audit_records
FROM repair_calculations;

-- 11. 显示修复预览（按产品和库位分组显示）
SELECT
    '修复预览' AS action,
    log_id,
    product_version_id,
    bin_location_detail_id,
    qty_side,
    change_type,
    chain_order,
    CASE WHEN is_break_point = 1 THEN '断链点' ELSE '' END AS break_info,
    CASE WHEN is_inventory_audit = 1 THEN '盘点' ELSE '' END AS audit_info,
    CONCAT(old_before_qty, ' -> ', new_before_qty) AS before_qty_change,
    CONCAT(old_after_qty, ' -> ', new_after_qty) AS after_qty_change,
    CASE
        WHEN old_change_qty != new_change_qty THEN CONCAT(old_change_qty, ' -> ', new_change_qty)
        ELSE CAST(old_change_qty AS CHAR)
    END AS change_qty_info
FROM repair_calculations
ORDER BY product_version_id, bin_location_detail_id, chain_order
LIMIT 100;

-- 12. 执行修复（取消注释以下代码来执行实际修复）
/*
-- 修复source侧数据
UPDATE frp_log_dev.bin_location_log l
INNER JOIN repair_calculations rc ON l.id = rc.log_id AND rc.qty_side = 'source'
SET
    l.source_before_in_stock_qty = rc.new_before_qty,
    l.source_after_in_stock_qty = rc.new_after_qty,
    l.source_change_in_stock_qty = rc.new_change_qty;

-- 修复dest侧数据
UPDATE frp_log_dev.bin_location_log l
INNER JOIN repair_calculations rc ON l.id = rc.log_id AND rc.qty_side = 'dest'
SET
    l.dest_before_in_stock_qty = rc.new_before_qty,
    l.dest_after_in_stock_qty = rc.new_after_qty,
    l.dest_change_in_stock_qty = rc.new_change_qty;
*/

-- 13. 最终修复统计
SELECT
    '最终修复统计' AS info,
    COUNT(*) AS total_records_to_repair,
    COUNT(DISTINCT product_version_id) AS affected_products,
    COUNT(DISTINCT bin_location_detail_id) AS affected_locations,
    COUNT(DISTINCT CASE WHEN qty_side = 'source' THEN log_id END) AS source_records,
    COUNT(DISTINCT CASE WHEN qty_side = 'dest' THEN log_id END) AS dest_records,
    SUM(is_break_point) AS break_point_records,
    SUM(is_inventory_audit) AS inventory_audit_records,
    COUNT(*) - (SELECT COUNT(*) FROM broken_chains_result) AS additional_repaired_records
FROM repair_calculations;

-- 14. 按产品统计修复范围
SELECT
    '按产品统计修复范围' AS info,
    product_version_id,
    COUNT(*) AS total_records,
    SUM(is_break_point) AS break_points,
    SUM(is_inventory_audit) AS inventory_audits,
    COUNT(DISTINCT bin_location_detail_id) AS affected_locations
FROM repair_calculations
GROUP BY product_version_id
ORDER BY total_records DESC;

-- 清理临时表（如果不需要查看结果可以取消注释）
DROP TABLE IF EXISTS temp_iteration;
-- DROP TABLE IF EXISTS broken_chains_result;
-- DROP TABLE IF EXISTS full_chain_data;
-- DROP TABLE IF EXISTS repair_calculations;

-- 注意：
-- 1. 此脚本会修复从断链点开始到下一个InventoryAudit操作为止的所有记录
-- 2. 脚本首先会显示修复预览，不会立即执行修复
-- 3. 要执行实际修复，请取消注释第12步中的UPDATE语句
-- 4. 建议在执行修复前备份相关数据
-- 5. 修复完成后可以删除临时表
-- 6. 如果链路很长，可能需要增加迭代次数（第9.4步）
