package cn.need.cloud.biz.service.log.impl;

import cn.need.cloud.biz.cache.bean.BinLocationCache;
import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.converter.log.BinLocationLogConverter;
import cn.need.cloud.biz.mapper.log.BinLocationLogMapper;
import cn.need.cloud.biz.model.entity.base.RefNumModel;
import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetail;
import cn.need.cloud.biz.model.entity.log.BinLocationLog;
import cn.need.cloud.biz.model.query.log.BinLocationLogQuery;
import cn.need.cloud.biz.model.vo.binlocation.BinLocationChangeVO;
import cn.need.cloud.biz.model.vo.log.BinLocationLogPageVO;
import cn.need.cloud.biz.model.vo.log.BinLocationLogVO;
import cn.need.cloud.biz.service.log.BinLocationLogService;
import cn.need.cloud.biz.util.BinLocationCacheUtil;
import cn.need.cloud.biz.util.JsonUtil;
import cn.need.cloud.biz.util.ProductCacheUtil;
import cn.need.cloud.biz.util.WarehouseCacheUtil;
import cn.need.cloud.biz.util.log.AuditLogUtil;
import cn.need.framework.common.core.constant.DataState;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-27
 */
@DS("log_master")
@Service
public class BinLocationLogServiceImpl extends SuperServiceImpl<BinLocationLogMapper, BinLocationLog> implements BinLocationLogService {
    @Resource
    @Lazy
    private BinLocationLogService self;

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public int insert(BinLocationLog binLocationLog) {
        // 执行日志信息的插入操作
        return super.insert(binLocationLog);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public int insertBatch(Collection<? extends BinLocationLog> binLocationLogList) {
        if (ObjectUtil.isEmpty(binLocationLogList)) {
            return DataState.DISABLED;
        }
        log.debug("插入库位日志：{}", JsonUtil.toJson(binLocationLogList));
        // 执行日志信息的插入操作
        return super.insertBatch(binLocationLogList);
    }

    @Override
    public List<BinLocationLogPageVO> listByQuery(BinLocationLogQuery query) {
        List<BinLocationLogPageVO> dataList = mapper.listByQuery(query);

        //获取库位id
        Set<Long> binLocationIdList = dataList
                .stream()
                .flatMap(item -> Stream.of(item.getDestBinLocationId(), item.getSourceBinLocationId()))
                .collect(Collectors.toSet());
        //根据库位id映射库位名称
        Map<Long, String> nameMap = ObjectUtil.toMap(BinLocationCacheUtil.listByIds(binLocationIdList), BinLocationCache::getId, BinLocationCache::getLocationName);
        //遍历日志
        dataList.forEach(item -> {
            item.setSourceBinLocationName(nameMap.get(item.getSourceBinLocationId()));
            item.setDestBinLocationName(nameMap.get(item.getDestBinLocationId()));
        });

        //填充仓库基本信息
        WarehouseCacheUtil.filledWarehouse(dataList);
        //填充产品信息 - 使用辅助方法，确保在正确的数据源上下文中执行
        self.fillProductInfo(dataList);

        return dataList;
    }

    @Override
    public PageData<BinLocationLogPageVO> pageByQuery(PageSearch<BinLocationLogQuery> search) {
        //获取分页参数
        Page<BinLocationLog> page = Conditions.page(search, entityClass);
        //获取查询条件
        BinLocationLogQuery condition = search.getCondition();
        //获取分页列表
        List<BinLocationLogPageVO> dataList = mapper.listByQuery(condition, page);
        //获取库位id
        Set<Long> binLocationIdList = dataList
                .stream()
                .flatMap(item -> Stream.of(item.getDestBinLocationId(), item.getSourceBinLocationId()))
                .collect(Collectors.toSet());
        //根据库位id映射库位名称
        Map<Long, String> nameMap = ObjectUtil.toMap(BinLocationCacheUtil.listByIds(binLocationIdList), BinLocationCache::getId, BinLocationCache::getLocationName);
        //遍历日志
        dataList.forEach(item -> {
            item.setSourceBinLocationName(nameMap.get(item.getSourceBinLocationId()));
            item.setDestBinLocationName(nameMap.get(item.getDestBinLocationId()));
        });

        //填充仓库基本信息
        WarehouseCacheUtil.filledWarehouse(dataList);
        //填充产品信息 - 使用辅助方法，确保在正确的数据源上下文中执行
        self.fillProductInfo(dataList);

        //返回查询条件
        return new PageData<>(dataList, page);
    }

    /**
     * 填充产品信息的辅助方法
     * 通过显式指定数据源，确保在正确的数据源上下文中执行产品信息查询
     * 
     * @param dataList 需要填充产品信息的数据列表
     */
    @DS("master")
    public void fillProductInfo(List<BinLocationLogPageVO> dataList) {
        // 在master数据源上下文中执行产品信息填充
        ProductCacheUtil.filledProduct(dataList);
    }

    @Override
    public BinLocationLogVO detailById(Long id) {
        BinLocationLog entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            // throw new BusinessException("id: " + id + " not found in BinLocationLog");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "BinLocationLog", id));
        }
        return buildBinLocationLogVO(entity);
    }

    /**
     * 保存日志信息到数据库
     * 此方法使用了数据源动态切换机制，以便将日志信息保存到指定的数据库中
     * 选择特定的数据源进行操作是基于日志记录的特殊需求，例如，确保日志数据和主业务数据分离，
     * 提高系统的可维护性和性能
     *
     * @param binLocationLog 要保存的日志对象，包含了日志的所有必要信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void save(BinLocationLog binLocationLog) {
        // 执行日志信息的插入操作
        mapper.insert(binLocationLog);
    }

    /**
     * 保存日志信息到数据库
     * 此方法使用了数据源动态切换机制，以便将日志信息保存到指定的数据库中
     * 选择特定的数据源进行操作是基于日志记录的特殊需求，例如，确保日志数据和主业务数据分离，
     * 提高系统的可维护性和性能
     *
     * @param model    基类
     * @param source   起始库位
     * @param changeVO 变化信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void save(RefNumModel model, BinLocationDetail source, BinLocationChangeVO changeVO) {
        //持久化库位日志
        self.insert(buildBinLocationLog(model, source, changeVO));
    }

    @Override
    public BinLocationLog buildBinLocationLog(RefNumModel model, BinLocationDetail dest, BinLocationChangeVO changeVO) {
        //判空
        return AuditLogUtil.binLocationLog(model, dest)
                .with(BinLocationLog::setSourceAfterInStockQty, dest.getInStockQty())
                .with(BinLocationLog::setSourceChangeInStockQty, changeVO.getChangeInStockQty())
                .with(BinLocationLog::setSourceBeforeInStockQty, dest.getInStockQty() - changeVO.getChangeInStockQty())
                .with(BinLocationLog::setDestAfterInStockQty, dest.getInStockQty())
                .with(BinLocationLog::setDestChangeInStockQty, changeVO.getChangeInStockQty())
                .with(BinLocationLog::setDestBeforeInStockQty, dest.getInStockQty() - changeVO.getChangeInStockQty())
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void save(RefNumModel model, BinLocationDetail source, BinLocationChangeVO changeVO, String changeType) {
        //持久化库位日志
        self.insert(buildBinLocationLogWithChangeType(model, source, changeVO, changeType));
    }

    @Override
    public BinLocationLog buildBinLocationLogWithChangeType(RefNumModel model, BinLocationDetail dest, BinLocationChangeVO changeVO, String changeType) {
        //判空
        return AuditLogUtil.binLocationLog(model, dest)
                .with(BinLocationLog::setSourceAfterInStockQty, dest.getInStockQty())
                .with(BinLocationLog::setSourceChangeInStockQty, changeVO.getChangeInStockQty())
                .with(BinLocationLog::setSourceBeforeInStockQty, dest.getInStockQty() - changeVO.getChangeInStockQty())
                .with(BinLocationLog::setDestAfterInStockQty, dest.getInStockQty())
                .with(BinLocationLog::setDestChangeInStockQty, changeVO.getChangeInStockQty())
                .with(BinLocationLog::setDestBeforeInStockQty, dest.getInStockQty() - changeVO.getChangeInStockQty())
                .with(BinLocationLog::setChangeType, changeType)
                .build();
    }

    /**
     * 构建VO对象
     *
     * @param entity 对象
     * @return 返回包含详细信息的VO对象
     */
    private BinLocationLogVO buildBinLocationLogVO(BinLocationLog entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的VO对象
        return Converters.get(BinLocationLogConverter.class).toVO(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> repairBinLocationLogChain(Long productVersionId, Long binLocationDetailId) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 1. 检测断链记录
            List<Map<String, Object>> brokenChains = detectBrokenChains(productVersionId, binLocationDetailId);

            if (brokenChains.isEmpty()) {
                result.put("success", true);
                result.put("message", "没有发现断链记录");
                result.put("repairedCount", 0);
                return result;
            }

            // 2. 按产品版本和库位详情分组
            Map<String, List<Map<String, Object>>> groupedChains = brokenChains.stream()
                .collect(Collectors.groupingBy(record ->
                    record.get("product_version_id") + "_" + record.get("bin_location_detail_id")));

            int totalRepaired = 0;

            // 3. 对每个分组进行修复
            for (Map.Entry<String, List<Map<String, Object>>> entry : groupedChains.entrySet()) {
                List<Map<String, Object>> chainRecords = entry.getValue();

                // 按时间排序
                chainRecords.sort((a, b) -> {
                    int timeCompare = ((Date) a.get("create_time")).compareTo((Date) b.get("create_time"));
                    if (timeCompare != 0) return timeCompare;
                    return ((Long) a.get("log_id")).compareTo((Long) b.get("log_id"));
                });

                // 修复链路
                int repaired = repairChainGroup(chainRecords);
                totalRepaired += repaired;
            }

            result.put("success", true);
            result.put("message", "修复完成");
            result.put("repairedCount", totalRepaired);
            result.put("brokenChainsFound", brokenChains.size());

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "修复失败: " + e.getMessage());
            result.put("repairedCount", 0);
            throw e;
        }

        return result;
    }

    @Override
    public List<Map<String, Object>> detectBrokenChains(Long productVersionId, Long binLocationDetailId) {
        return mapper.detectBrokenChains(productVersionId, binLocationDetailId);
    }

    /**
     * 修复单个链路组 - 从断链点修复到InventoryAudit
     */
    private int repairChainGroup(List<Map<String, Object>> brokenChainRecords) {
        if (brokenChainRecords.isEmpty()) {
            return 0;
        }

        // 按产品版本和库位分组
        Map<String, List<Map<String, Object>>> groupedByLocation = brokenChainRecords.stream()
            .collect(Collectors.groupingBy(record ->
                record.get("product_version_id") + "_" + record.get("bin_location_detail_id")));

        int totalRepaired = 0;

        for (Map.Entry<String, List<Map<String, Object>>> entry : groupedByLocation.entrySet()) {
            List<Map<String, Object>> locationBrokenChains = entry.getValue();

            // 获取第一个断链记录的信息
            Map<String, Object> firstBrokenRecord = locationBrokenChains.get(0);
            Long productVersionId = (Long) firstBrokenRecord.get("product_version_id");
            Long binLocationDetailId = (Long) firstBrokenRecord.get("bin_location_detail_id");

            // 获取从断链点到InventoryAudit的完整链路
            List<Map<String, Object>> fullChainToRepair = getFullChainToRepair(
                productVersionId, binLocationDetailId, locationBrokenChains);

            if (!fullChainToRepair.isEmpty()) {
                int repaired = repairFullChain(fullChainToRepair, locationBrokenChains);
                totalRepaired += repaired;
            }
        }

        return totalRepaired;
    }

    /**
     * 获取从断链点到InventoryAudit的完整链路
     */
    private List<Map<String, Object>> getFullChainToRepair(Long productVersionId, Long binLocationDetailId,
                                                           List<Map<String, Object>> brokenChains) {
        // 找到最小的断链点位置
        int minBreakOrder = brokenChains.stream()
            .mapToInt(record -> (Integer) record.get("chain_order"))
            .min().orElse(1);

        // 调用mapper方法获取完整链路数据
        return mapper.getFullChainData(productVersionId, binLocationDetailId, minBreakOrder);
    }

    /**
     * 修复完整链路
     */
    private int repairFullChain(List<Map<String, Object>> fullChain, List<Map<String, Object>> brokenChains) {
        if (fullChain.isEmpty()) {
            return 0;
        }

        List<Map<String, Object>> updateList = new ArrayList<>();

        // 获取断链点的正确起始值
        Map<String, Object> firstBrokenRecord = brokenChains.get(0);
        Integer correctAfterQty = (Integer) firstBrokenRecord.get("prev_after_qty");

        // 逐个修复链路中的记录
        for (Map<String, Object> record : fullChain) {
            Integer changeQty = (Integer) record.get("change_qty");
            String changeType = (String) record.get("change_type");

            // 计算正确的before_qty和after_qty
            Integer correctBeforeQty = correctAfterQty;
            Integer correctAfterQtyNew;
            Integer correctChangeQty = changeQty;

            // 如果是InventoryAudit操作，特殊处理
            if ("InventoryAudit".equals(changeType)) {
                correctAfterQtyNew = correctBeforeQty; // before_qty = after_qty
                correctChangeQty = 0; // change_qty = 0
            } else {
                correctAfterQtyNew = correctBeforeQty + changeQty;
            }

            // 准备更新数据
            Map<String, Object> updateRecord = new HashMap<>();
            updateRecord.put("log_id", record.get("log_id"));
            updateRecord.put("qty_side", record.get("qty_side"));
            updateRecord.put("new_before_qty", correctBeforeQty);
            updateRecord.put("new_after_qty", correctAfterQtyNew);
            updateRecord.put("new_change_qty", correctChangeQty);

            updateList.add(updateRecord);

            // 更新correctAfterQty为下一个记录使用
            correctAfterQty = correctAfterQtyNew;

            // 如果遇到InventoryAudit，停止修复
            if ("InventoryAudit".equals(changeType)) {
                break;
            }
        }

        // 批量更新 - 按source和dest分组
        if (!updateList.isEmpty()) {
            List<Map<String, Object>> sourceUpdates = updateList.stream()
                .filter(record -> "source".equals(record.get("qty_side")))
                .collect(Collectors.toList());

            List<Map<String, Object>> destUpdates = updateList.stream()
                .filter(record -> "dest".equals(record.get("qty_side")))
                .collect(Collectors.toList());

            if (!sourceUpdates.isEmpty()) {
                mapper.batchUpdateSourceQtyFields(sourceUpdates);
            }

            if (!destUpdates.isEmpty()) {
                mapper.batchUpdateDestQtyFields(destUpdates);
            }
        }

        return updateList.size();
    }
}
