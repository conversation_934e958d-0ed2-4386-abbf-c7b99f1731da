package cn.need.cloud.biz.controller.log;

import cn.need.cloud.biz.service.log.BinLocationLogDataFixService;
import cn.need.cloud.biz.service.log.BinLocationLogDataFixService.DataFixResult;
import cn.need.cloud.biz.service.log.BinLocationLogDataFixService.DataAnalysisResult;
import cn.need.cloud.biz.service.log.BinLocationLogDataFixService.DataValidationResult;
import cn.need.cloud.biz.service.log.BinLocationLogDataFixService.AdvancedChainFixResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;

/**
 * BinLocationLog 数据修复控制器
 * 
 * 提供数据修复相关的接口
 */
@Slf4j
@RestController
@RequestMapping("/binlocationlog/datafix")
@Api(tags = "BinLocationLog数据修复")
@RequiredArgsConstructor
public class BinLocationLogDataFixController {

    private final BinLocationLogDataFixService dataFixService;

    @GetMapping("/analyze")
    @ApiOperation("分析数据问题")
    public DataAnalysisResult analyzeDataProblems(
            @ApiParam("起始时间，格式：yyyy-MM-dd HH:mm:ss")
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime) {
        
        log.info("开始分析 BinLocationLog 数据问题，起始时间: {}", startTime);
        return dataFixService.analyzeDataProblems(startTime);
    }

    @PostMapping("/fix/inventory-audit")
    @ApiOperation("修复 inventoryaudit 数据")
    public Integer fixInventoryAuditData(
            @ApiParam("起始时间，格式：yyyy-MM-dd HH:mm:ss")
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime) {
        
        log.info("开始修复 inventoryaudit 数据，起始时间: {}", startTime);
        return dataFixService.fixInventoryAuditData(startTime);
    }

    @PostMapping("/fix/chain-continuity")
    @ApiOperation("重建链条连续性")
    public Integer rebuildChainContinuity(
            @ApiParam("起始时间，格式：yyyy-MM-dd HH:mm:ss")
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime) {
        
        log.info("开始重建链条连续性，起始时间: {}", startTime);
        return dataFixService.rebuildChainContinuity(startTime);
    }

    @GetMapping("/validate")
    @ApiOperation("验证修复结果")
    public DataValidationResult validateFixResult(
            @ApiParam("起始时间，格式：yyyy-MM-dd HH:mm:ss")
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime) {
        
        log.info("开始验证修复结果，起始时间: {}", startTime);
        return dataFixService.validateFixResult(startTime);
    }

    @PostMapping("/fix/full")
    @ApiOperation("执行完整修复流程")
    public DataFixResult executeFullDataFix(
            @ApiParam("起始时间，格式：yyyy-MM-dd HH:mm:ss")
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime) {
        
        log.info("开始执行完整数据修复流程，起始时间: {}", startTime);
        return dataFixService.executeFullDataFix(startTime);
    }

    @PostMapping("/fix/advanced-chain-repair")
    @ApiOperation("高级链条修复 - 从断链处修复到InventoryAudit")
    public AdvancedChainFixResult repairBrokenChainsToInventoryAudit(
            @ApiParam("起始时间，格式：yyyy-MM-dd HH:mm:ss")
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime) {
        
        log.info("开始执行高级链条修复，起始时间: {}", startTime);
        return dataFixService.repairBrokenChainsToInventoryAudit(startTime);
    }

    @PostMapping("/fix/step-by-step")
    @ApiOperation("分步骤执行修复")
    public StepByStepFixResult executeStepByStepFix(
            @ApiParam("起始时间，格式：yyyy-MM-dd HH:mm:ss")
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime) {
        
        log.info("开始分步骤执行数据修复，起始时间: {}", startTime);
        
        StepByStepFixResult result = new StepByStepFixResult();
        
        try {
            // 步骤1：分析问题
            log.info("步骤1：分析数据问题");
            result.setStep1Analysis(dataFixService.analyzeDataProblems(startTime));
            
            // 步骤2：修复 inventoryaudit
            log.info("步骤2：修复 inventoryaudit 数据");
            result.setStep2InventoryAuditFix(dataFixService.fixInventoryAuditData(startTime));
            
            // 步骤3：高级链条修复
            log.info("步骤3：执行高级链条修复");
            result.setStep3AdvancedChainRepair(dataFixService.repairBrokenChainsToInventoryAudit(startTime));
            
            // 步骤4：验证结果
            log.info("步骤4：验证修复结果");
            result.setStep4Validation(dataFixService.validateFixResult(startTime));
            
            result.setSuccess(true);
            log.info("分步骤数据修复完成: {}", result);
            
        } catch (Exception e) {
            log.error("分步骤数据修复失败", e);
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
        }
        
        return result;
    }

    /**
     * 分步骤修复结果
     */
    public static class StepByStepFixResult {
        private boolean success;
        private String errorMessage;
        private DataAnalysisResult step1Analysis;
        private Integer step2InventoryAuditFix;
        private AdvancedChainFixResult step3AdvancedChainRepair;
        private DataValidationResult step4Validation;
        
        // getters and setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        public DataAnalysisResult getStep1Analysis() { return step1Analysis; }
        public void setStep1Analysis(DataAnalysisResult step1Analysis) { this.step1Analysis = step1Analysis; }
        public Integer getStep2InventoryAuditFix() { return step2InventoryAuditFix; }
        public void setStep2InventoryAuditFix(Integer step2InventoryAuditFix) { this.step2InventoryAuditFix = step2InventoryAuditFix; }
        public AdvancedChainFixResult getStep3AdvancedChainRepair() { return step3AdvancedChainRepair; }
        public void setStep3AdvancedChainRepair(AdvancedChainFixResult step3AdvancedChainRepair) { this.step3AdvancedChainRepair = step3AdvancedChainRepair; }
        public DataValidationResult getStep4Validation() { return step4Validation; }
        public void setStep4Validation(DataValidationResult step4Validation) { this.step4Validation = step4Validation; }
        
        @Override
        public String toString() {
            return String.format("StepByStepFixResult{success=%s, analysis=%s, auditFix=%d, advancedRepair=%s, validation=%s}", 
                success, 
                step1Analysis != null ? step1Analysis.toString() : "null",
                step2InventoryAuditFix != null ? step2InventoryAuditFix : 0,
                step3AdvancedChainRepair != null ? step3AdvancedChainRepair.toString() : "null",
                step4Validation != null ? step4Validation.isOverallValid() : "unknown");
        }
    }
}
