package cn.need.cloud.biz.service.log;

import cn.need.cloud.biz.model.entity.base.RefNumModel;
import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetail;
import cn.need.cloud.biz.model.entity.log.BinLocationLog;
import cn.need.cloud.biz.model.query.log.BinLocationLogQuery;
import cn.need.cloud.biz.model.vo.binlocation.BinLocationChangeVO;
import cn.need.cloud.biz.model.vo.log.BinLocationLogPageVO;
import cn.need.cloud.biz.model.vo.log.BinLocationLogVO;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-27
 */
public interface BinLocationLogService extends SuperService<BinLocationLog> {

    /**
     * 根据查询条件获取列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个对象的列表(分页)
     */
    List<BinLocationLogPageVO> listByQuery(BinLocationLogQuery query);

    /**
     * 根据查询条件获取列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个对象的列表(分页)
     */
    PageData<BinLocationLogPageVO> pageByQuery(PageSearch<BinLocationLogQuery> search);


    /**
     * 根据ID获取
     *
     * @param id ID
     * @return 返回VO对象
     */
    BinLocationLogVO detailById(Long id);

    /**
     * 保存库存日志
     *
     * @param binLocationLog 库存持久化实体
     */
    void save(BinLocationLog binLocationLog);

    /**
     * 保存库存日志
     *
     * @param model    库存持久化实体
     * @param source   库存持久化实体
     * @param changeVO 库存持久化实体
     */
    void save(RefNumModel model, BinLocationDetail source, BinLocationChangeVO changeVO);

    /**
     * 构建库位日志
     *
     * @param model    基类
     * @param dest     目的库位
     * @param changeVO 变化库位信息
     * @return 库位日志
     */
    BinLocationLog buildBinLocationLog(RefNumModel model, BinLocationDetail dest, BinLocationChangeVO changeVO);


    /**
     * 保存有ChangeType的库存日志
     *
     * @param model    库存持久化实体
     * @param source   库存持久化实体
     * @param changeVO 库存持久化实体
     */
    void save(RefNumModel model, BinLocationDetail source, BinLocationChangeVO changeVO, String changeType);

    /**
     * 构建有ChangeType的库位日志
     *
     * @param model    基类
     * @param dest     目的库位
     * @param changeVO 变化库位信息
     * @return 库位日志
     */
    BinLocationLog buildBinLocationLogWithChangeType(RefNumModel model, BinLocationDetail dest, BinLocationChangeVO changeVO, String changeType);
    
    /**
     * 填充产品信息的辅助方法
     * 通过显式指定数据源，确保在正确的数据源上下文中执行产品信息查询
     *
     * @param dataList 需要填充产品信息的数据列表
     */
    void fillProductInfo(List<BinLocationLogPageVO> dataList);

    /**
     * 检测并修复BinLocationLog链路断链问题
     *
     * @param productVersionId 产品版本ID，如果为null则修复所有产品
     * @param binLocationDetailId 库位详情ID，如果为null则修复所有库位
     * @return 修复结果统计
     */
    Map<String, Object> repairBinLocationLogChain(Long productVersionId, Long binLocationDetailId);

    /**
     * 检测BinLocationLog链路断链问题
     *
     * @param productVersionId 产品版本ID，如果为null则检测所有产品
     * @param binLocationDetailId 库位详情ID，如果为null则检测所有库位
     * @return 断链检测结果
     */
    List<Map<String, Object>> detectBrokenChains(Long productVersionId, Long binLocationDetailId);
}