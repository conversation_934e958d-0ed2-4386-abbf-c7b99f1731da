package cn.need.cloud.biz.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * BinLocationLog链路修复工具类 - 完整链路修复版本
 * 从断链点修复到InventoryAudit操作
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
@Slf4j
@Component
public class BinLocationLogChainRepairUtil {

    /**
     * 执行完整链路修复
     * 
     * @param dataSource 数据源
     * @param dryRun 是否为试运行模式
     * @return 修复结果
     */
    public Map<String, Object> repairChainFromBreakToAudit(DataSource dataSource, boolean dryRun) {
        Map<String, Object> result = new HashMap<>();
        
        try (Connection connection = dataSource.getConnection()) {
            connection.setAutoCommit(false);
            
            try {
                // 1. 检测断链记录
                List<Map<String, Object>> brokenChains = detectBrokenChains(connection);
                result.put("broken_chains_count", brokenChains.size());
                
                if (brokenChains.isEmpty()) {
                    result.put("success", true);
                    result.put("message", "未发现断链记录");
                    result.put("repaired_count", 0);
                    return result;
                }
                
                // 2. 获取需要修复的完整链路
                List<Map<String, Object>> fullChainToRepair = getFullChainToRepair(connection, brokenChains);
                result.put("full_chain_count", fullChainToRepair.size());
                
                // 3. 计算修复数值
                List<Map<String, Object>> repairData = calculateRepairValues(brokenChains, fullChainToRepair);
                result.put("repair_data_count", repairData.size());
                
                // 4. 执行修复（如果不是试运行）
                if (!dryRun && !repairData.isEmpty()) {
                    int repairedCount = executeRepair(connection, repairData);
                    result.put("repaired_count", repairedCount);
                    connection.commit();
                } else {
                    result.put("repaired_count", 0);
                    result.put("dry_run", true);
                }
                
                result.put("success", true);
                result.put("message", "修复完成");
                result.put("repair_preview", repairData.subList(0, Math.min(10, repairData.size())));
                
            } catch (Exception e) {
                connection.rollback();
                throw e;
            }
            
        } catch (Exception e) {
            log.error("链路修复失败", e);
            result.put("success", false);
            result.put("message", "修复失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 检测断链记录
     */
    private List<Map<String, Object>> detectBrokenChains(Connection connection) throws Exception {
        String sql = """
            WITH qty_union AS (
                SELECT
                    l.id AS log_id,
                    l.product_version_id,
                    l.source_bin_location_detail_id AS bin_location_detail_id,
                    l.create_time,
                    'source' AS qty_side,
                    l.source_before_in_stock_qty AS before_qty,
                    l.source_change_in_stock_qty AS change_qty,
                    l.source_after_in_stock_qty AS after_qty,
                    l.change_type
                FROM bin_location_log l
                WHERE l.remove_flag = 0
                UNION ALL
                SELECT
                    l.id,
                    l.product_version_id,
                    l.dest_bin_location_detail_id,
                    l.create_time,
                    'dest',
                    l.dest_before_in_stock_qty,
                    l.dest_change_in_stock_qty,
                    l.dest_after_in_stock_qty,
                    l.change_type
                FROM bin_location_log l
                WHERE NOT (
                    l.dest_bin_location_detail_id = l.source_bin_location_detail_id
                    AND l.dest_change_in_stock_qty = l.source_change_in_stock_qty
                ) AND l.remove_flag = 0
            ),
            qty_chain AS (
                SELECT
                    q.*,
                    LAG(q.after_qty) OVER (
                        PARTITION BY q.product_version_id, q.bin_location_detail_id
                        ORDER BY q.create_time, q.log_id, q.qty_side
                    ) AS prev_after_qty,
                    ROW_NUMBER() OVER (
                        PARTITION BY q.product_version_id, q.bin_location_detail_id
                        ORDER BY q.create_time, q.log_id, q.qty_side
                    ) AS chain_order
                FROM qty_union q
            )
            SELECT
                qc.log_id,
                qc.product_version_id,
                qc.bin_location_detail_id,
                qc.qty_side,
                qc.before_qty,
                qc.change_qty,
                qc.after_qty,
                qc.prev_after_qty,
                qc.change_type,
                qc.create_time,
                qc.chain_order
            FROM qty_chain qc
            WHERE qc.prev_after_qty IS NOT NULL
              AND qc.prev_after_qty <> qc.before_qty
            ORDER BY qc.product_version_id, qc.bin_location_detail_id, qc.chain_order
            """;
        
        List<Map<String, Object>> result = new ArrayList<>();
        try (PreparedStatement stmt = connection.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                Map<String, Object> record = new HashMap<>();
                record.put("log_id", rs.getLong("log_id"));
                record.put("product_version_id", rs.getLong("product_version_id"));
                record.put("bin_location_detail_id", rs.getLong("bin_location_detail_id"));
                record.put("qty_side", rs.getString("qty_side"));
                record.put("before_qty", rs.getInt("before_qty"));
                record.put("change_qty", rs.getInt("change_qty"));
                record.put("after_qty", rs.getInt("after_qty"));
                record.put("prev_after_qty", rs.getInt("prev_after_qty"));
                record.put("change_type", rs.getString("change_type"));
                record.put("create_time", rs.getTimestamp("create_time"));
                record.put("chain_order", rs.getInt("chain_order"));
                result.add(record);
            }
        }
        
        log.info("检测到断链记录数: {}", result.size());
        return result;
    }

    /**
     * 获取需要修复的完整链路（从断链点到InventoryAudit）
     */
    private List<Map<String, Object>> getFullChainToRepair(Connection connection, 
                                                           List<Map<String, Object>> brokenChains) throws Exception {
        if (brokenChains.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 按产品和库位分组获取断链点信息
        Map<String, Integer> breakPoints = new HashMap<>();
        for (Map<String, Object> chain : brokenChains) {
            String key = chain.get("product_version_id") + "_" + chain.get("bin_location_detail_id");
            Integer currentOrder = (Integer) chain.get("chain_order");
            breakPoints.merge(key, currentOrder, Integer::min);
        }
        
        List<Map<String, Object>> result = new ArrayList<>();
        
        // 为每个断链点获取完整链路
        for (Map.Entry<String, Integer> entry : breakPoints.entrySet()) {
            String[] parts = entry.getKey().split("_");
            Long productVersionId = Long.parseLong(parts[0]);
            Long binLocationDetailId = Long.parseLong(parts[1]);
            Integer startOrder = entry.getValue();
            
            List<Map<String, Object>> chainData = getChainDataFromOrder(
                connection, productVersionId, binLocationDetailId, startOrder);
            result.addAll(chainData);
        }
        
        log.info("获取到需要修复的完整链路记录数: {}", result.size());
        return result;
    }

    /**
     * 获取从指定顺序开始到下一个InventoryAudit的链路数据
     */
    private List<Map<String, Object>> getChainDataFromOrder(Connection connection, 
                                                            Long productVersionId, 
                                                            Long binLocationDetailId, 
                                                            Integer startOrder) throws Exception {
        String sql = """
            WITH qty_union AS (
                SELECT
                    l.id AS log_id,
                    l.product_version_id,
                    l.source_bin_location_detail_id AS bin_location_detail_id,
                    l.create_time,
                    'source' AS qty_side,
                    l.source_before_in_stock_qty AS before_qty,
                    l.source_change_in_stock_qty AS change_qty,
                    l.source_after_in_stock_qty AS after_qty,
                    l.change_type
                FROM bin_location_log l
                WHERE l.remove_flag = 0
                  AND l.product_version_id = ?
                  AND l.source_bin_location_detail_id = ?
                UNION ALL
                SELECT
                    l.id,
                    l.product_version_id,
                    l.dest_bin_location_detail_id,
                    l.create_time,
                    'dest',
                    l.dest_before_in_stock_qty,
                    l.dest_change_in_stock_qty,
                    l.dest_after_in_stock_qty,
                    l.change_type
                FROM bin_location_log l
                WHERE NOT (
                    l.dest_bin_location_detail_id = l.source_bin_location_detail_id
                    AND l.dest_change_in_stock_qty = l.source_change_in_stock_qty
                ) AND l.remove_flag = 0
                  AND l.product_version_id = ?
                  AND l.dest_bin_location_detail_id = ?
            ),
            qty_chain AS (
                SELECT
                    q.*,
                    ROW_NUMBER() OVER (
                        PARTITION BY q.product_version_id, q.bin_location_detail_id
                        ORDER BY q.create_time, q.log_id, q.qty_side
                    ) AS chain_order
                FROM qty_union q
            )
            SELECT
                qc.log_id,
                qc.product_version_id,
                qc.bin_location_detail_id,
                qc.qty_side,
                qc.before_qty,
                qc.change_qty,
                qc.after_qty,
                qc.change_type,
                qc.create_time,
                qc.chain_order
            FROM qty_chain qc
            WHERE qc.chain_order >= ?
              AND qc.chain_order <= COALESCE(
                  (SELECT MIN(qc2.chain_order) 
                   FROM qty_chain qc2 
                   WHERE qc2.product_version_id = qc.product_version_id
                     AND qc2.bin_location_detail_id = qc.bin_location_detail_id
                     AND qc2.chain_order > ?
                     AND qc2.change_type = 'InventoryAudit'),
                  999999
              )
            ORDER BY qc.chain_order
            """;
        
        List<Map<String, Object>> result = new ArrayList<>();
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setLong(1, productVersionId);
            stmt.setLong(2, binLocationDetailId);
            stmt.setLong(3, productVersionId);
            stmt.setLong(4, binLocationDetailId);
            stmt.setInt(5, startOrder);
            stmt.setInt(6, startOrder);
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    Map<String, Object> record = new HashMap<>();
                    record.put("log_id", rs.getLong("log_id"));
                    record.put("product_version_id", rs.getLong("product_version_id"));
                    record.put("bin_location_detail_id", rs.getLong("bin_location_detail_id"));
                    record.put("qty_side", rs.getString("qty_side"));
                    record.put("before_qty", rs.getInt("before_qty"));
                    record.put("change_qty", rs.getInt("change_qty"));
                    record.put("after_qty", rs.getInt("after_qty"));
                    record.put("change_type", rs.getString("change_type"));
                    record.put("create_time", rs.getTimestamp("create_time"));
                    record.put("chain_order", rs.getInt("chain_order"));
                    result.add(record);
                }
            }
        }
        
        return result;
    }

    /**
     * 计算修复数值
     */
    private List<Map<String, Object>> calculateRepairValues(List<Map<String, Object>> brokenChains,
                                                            List<Map<String, Object>> fullChain) {
        List<Map<String, Object>> repairData = new ArrayList<>();

        // 按产品和库位分组处理
        Map<String, List<Map<String, Object>>> groupedChains = new HashMap<>();
        for (Map<String, Object> record : fullChain) {
            String key = record.get("product_version_id") + "_" + record.get("bin_location_detail_id");
            groupedChains.computeIfAbsent(key, k -> new ArrayList<>()).add(record);
        }

        // 获取断链点的正确起始值
        Map<String, Integer> breakPointCorrectValues = new HashMap<>();
        for (Map<String, Object> brokenChain : brokenChains) {
            String key = brokenChain.get("product_version_id") + "_" +
                        brokenChain.get("bin_location_detail_id") + "_" +
                        brokenChain.get("qty_side");
            Integer prevAfterQty = (Integer) brokenChain.get("prev_after_qty");
            breakPointCorrectValues.put(key, prevAfterQty);
        }

        // 为每个分组计算修复数值
        for (Map.Entry<String, List<Map<String, Object>>> entry : groupedChains.entrySet()) {
            List<Map<String, Object>> chainRecords = entry.getValue();
            chainRecords.sort((a, b) -> Integer.compare((Integer) a.get("chain_order"), (Integer) b.get("chain_order")));

            // 按qty_side分组
            Map<String, List<Map<String, Object>>> sideGroups = new HashMap<>();
            for (Map<String, Object> record : chainRecords) {
                String side = (String) record.get("qty_side");
                sideGroups.computeIfAbsent(side, k -> new ArrayList<>()).add(record);
            }

            // 为每个side计算修复数值
            for (Map.Entry<String, List<Map<String, Object>>> sideEntry : sideGroups.entrySet()) {
                String side = sideEntry.getKey();
                List<Map<String, Object>> sideRecords = sideEntry.getValue();

                String breakKey = entry.getKey() + "_" + side;
                Integer correctAfterQty = breakPointCorrectValues.get(breakKey);

                if (correctAfterQty != null) {
                    for (Map<String, Object> record : sideRecords) {
                        Map<String, Object> repairRecord = calculateSingleRecordRepair(record, correctAfterQty);
                        repairData.add(repairRecord);

                        // 更新correctAfterQty为下一个记录使用
                        correctAfterQty = (Integer) repairRecord.get("new_after_qty");

                        // 如果遇到InventoryAudit，停止修复
                        if ("InventoryAudit".equals(record.get("change_type"))) {
                            break;
                        }
                    }
                }
            }
        }

        return repairData;
    }

    /**
     * 计算单个记录的修复数值
     */
    private Map<String, Object> calculateSingleRecordRepair(Map<String, Object> record, Integer correctBeforeQty) {
        Map<String, Object> repairRecord = new HashMap<>();

        Long logId = (Long) record.get("log_id");
        String qtySide = (String) record.get("qty_side");
        String changeType = (String) record.get("change_type");
        Integer oldBeforeQty = (Integer) record.get("before_qty");
        Integer oldAfterQty = (Integer) record.get("after_qty");
        Integer oldChangeQty = (Integer) record.get("change_qty");

        Integer newBeforeQty = correctBeforeQty;
        Integer newAfterQty;
        Integer newChangeQty;

        // 如果是InventoryAudit操作，特殊处理
        if ("InventoryAudit".equals(changeType)) {
            newAfterQty = newBeforeQty; // before_qty = after_qty
            newChangeQty = 0; // change_qty = 0
        } else {
            newAfterQty = newBeforeQty + oldChangeQty;
            newChangeQty = oldChangeQty;
        }

        repairRecord.put("log_id", logId);
        repairRecord.put("qty_side", qtySide);
        repairRecord.put("old_before_qty", oldBeforeQty);
        repairRecord.put("old_after_qty", oldAfterQty);
        repairRecord.put("old_change_qty", oldChangeQty);
        repairRecord.put("new_before_qty", newBeforeQty);
        repairRecord.put("new_after_qty", newAfterQty);
        repairRecord.put("new_change_qty", newChangeQty);
        repairRecord.put("change_type", changeType);

        return repairRecord;
    }

    /**
     * 执行修复
     */
    private int executeRepair(Connection connection, List<Map<String, Object>> repairData) throws Exception {
        // 按source和dest分组
        List<Map<String, Object>> sourceUpdates = new ArrayList<>();
        List<Map<String, Object>> destUpdates = new ArrayList<>();

        for (Map<String, Object> record : repairData) {
            if ("source".equals(record.get("qty_side"))) {
                sourceUpdates.add(record);
            } else {
                destUpdates.add(record);
            }
        }

        int totalUpdated = 0;

        // 更新source侧数据
        if (!sourceUpdates.isEmpty()) {
            totalUpdated += batchUpdateSource(connection, sourceUpdates);
        }

        // 更新dest侧数据
        if (!destUpdates.isEmpty()) {
            totalUpdated += batchUpdateDest(connection, destUpdates);
        }

        log.info("执行修复完成，总共更新记录数: {}", totalUpdated);
        return totalUpdated;
    }

    /**
     * 批量更新source侧数据
     */
    private int batchUpdateSource(Connection connection, List<Map<String, Object>> updates) throws Exception {
        String sql = """
            UPDATE bin_location_log
            SET source_before_in_stock_qty = ?,
                source_after_in_stock_qty = ?,
                source_change_in_stock_qty = ?
            WHERE id = ?
            """;

        int count = 0;
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            for (Map<String, Object> record : updates) {
                stmt.setInt(1, (Integer) record.get("new_before_qty"));
                stmt.setInt(2, (Integer) record.get("new_after_qty"));
                stmt.setInt(3, (Integer) record.get("new_change_qty"));
                stmt.setLong(4, (Long) record.get("log_id"));
                stmt.addBatch();
                count++;
            }
            stmt.executeBatch();
        }

        return count;
    }

    /**
     * 批量更新dest侧数据
     */
    private int batchUpdateDest(Connection connection, List<Map<String, Object>> updates) throws Exception {
        String sql = """
            UPDATE bin_location_log
            SET dest_before_in_stock_qty = ?,
                dest_after_in_stock_qty = ?,
                dest_change_in_stock_qty = ?
            WHERE id = ?
            """;

        int count = 0;
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            for (Map<String, Object> record : updates) {
                stmt.setInt(1, (Integer) record.get("new_before_qty"));
                stmt.setInt(2, (Integer) record.get("new_after_qty"));
                stmt.setInt(3, (Integer) record.get("new_change_qty"));
                stmt.setLong(4, (Long) record.get("log_id"));
                stmt.addBatch();
                count++;
            }
            stmt.executeBatch();
        }

        return count;
    }
}
