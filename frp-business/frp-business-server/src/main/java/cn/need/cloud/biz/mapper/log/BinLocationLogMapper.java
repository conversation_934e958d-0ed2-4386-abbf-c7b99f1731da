package cn.need.cloud.biz.mapper.log;

import cn.need.cloud.biz.model.entity.log.BinLocationLog;
import cn.need.cloud.biz.model.query.log.BinLocationLogQuery;
import cn.need.cloud.biz.model.vo.log.BinLocationLogPageVO;
import cn.need.framework.common.mybatis.base.SuperMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-27
 */
@Mapper
public interface BinLocationLogMapper extends SuperMapper<BinLocationLog> {

    /**
     * 根据条件获取列表
     *
     * @param query 查询条件
     * @return 集合
     */
    default List<BinLocationLogPageVO> listByQuery(BinLocationLogQuery query) {
        return listByQuery(query, null);
    }

    /**
     * 根据条件获取分页列表
     *
     * @param query 查询条件
     * @param page  分页
     * @return 集合
     */
    List<BinLocationLogPageVO> listByQuery(@Param("qo") BinLocationLogQuery query, @Param("page") Page<?> page);

    /**
     * 检测断链的记录
     *
     * @param productVersionId 产品版本ID
     * @param binLocationDetailId 库位详情ID
     * @return 断链记录列表
     */
    List<Map<String, Object>> detectBrokenChains(@Param("productVersionId") Long productVersionId,
                                                  @Param("binLocationDetailId") Long binLocationDetailId);

    /**
     * 批量更新BinLocationLog记录 - Source侧
     *
     * @param updateList 需要更新的记录列表
     * @return 更新的记录数
     */
    int batchUpdateSourceQtyFields(@Param("updateList") List<Map<String, Object>> updateList);

    /**
     * 批量更新BinLocationLog记录 - Dest侧
     *
     * @param updateList 需要更新的记录列表
     * @return 更新的记录数
     */
    int batchUpdateDestQtyFields(@Param("updateList") List<Map<String, Object>> updateList);

    /**
     * 获取完整链路数据（从指定位置到下一个InventoryAudit）
     *
     * @param productVersionId 产品版本ID
     * @param binLocationDetailId 库位详情ID
     * @param startOrder 起始链路顺序
     * @return 完整链路数据
     */
    List<Map<String, Object>> getFullChainData(@Param("productVersionId") Long productVersionId,
                                               @Param("binLocationDetailId") Long binLocationDetailId,
                                               @Param("startOrder") Integer startOrder);
}