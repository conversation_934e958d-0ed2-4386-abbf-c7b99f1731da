package cn.need.cloud.biz.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.sql.*;
import java.util.*;

/**
 * BinLocationLog链路修复工具类
 * 
 * <AUTHOR>
 * @since 2025-01-07
 */
@Slf4j
@Component
public class BinLocationLogRepairUtil {

    @Resource
    private DataSource dataSource;

    /**
     * 执行完整的修复流程
     * 
     * @param dryRun 是否为试运行（不执行实际修复）
     * @return 修复结果
     */
    public Map<String, Object> executeFullRepair(boolean dryRun) {
        Map<String, Object> result = new HashMap<>();
        
        try (Connection connection = dataSource.getConnection()) {
            connection.setAutoCommit(false);
            
            try {
                // 1. 创建临时表
                createTempTable(connection);
                
                // 2. 检测断链并插入临时表
                int brokenRecords = detectAndInsertBrokenChains(connection);
                
                if (brokenRecords == 0) {
                    result.put("success", true);
                    result.put("message", "没有发现断链记录");
                    result.put("brokenRecords", 0);
                    result.put("repairedRecords", 0);
                    return result;
                }
                
                // 3. 计算修复数值
                calculateRepairValues(connection);
                
                // 4. 获取修复预览
                List<Map<String, Object>> preview = getRepairPreview(connection);
                
                int repairedRecords = 0;
                if (!dryRun) {
                    // 5. 执行实际修复
                    repairedRecords = executeActualRepair(connection);
                    connection.commit();
                } else {
                    connection.rollback();
                }
                
                // 6. 获取统计信息
                Map<String, Object> stats = getRepairStats(connection);
                
                result.put("success", true);
                result.put("message", dryRun ? "试运行完成" : "修复完成");
                result.put("brokenRecords", brokenRecords);
                result.put("repairedRecords", repairedRecords);
                result.put("preview", preview);
                result.put("stats", stats);
                
            } catch (Exception e) {
                connection.rollback();
                throw e;
            } finally {
                // 清理临时表
                dropTempTable(connection);
            }
            
        } catch (Exception e) {
            log.error("修复执行失败", e);
            result.put("success", false);
            result.put("message", "修复执行失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 创建临时表
     */
    private void createTempTable(Connection connection) throws SQLException {
        String sql = """
            DROP TABLE IF EXISTS temp_bin_location_log_repair;
            
            CREATE TABLE temp_bin_location_log_repair (
                log_id BIGINT NOT NULL,
                product_version_id BIGINT NOT NULL,
                bin_location_detail_id BIGINT NOT NULL,
                qty_side VARCHAR(10) NOT NULL,
                old_before_qty INT,
                old_after_qty INT,
                old_change_qty INT,
                new_before_qty INT,
                new_after_qty INT,
                new_change_qty INT,
                change_type VARCHAR(255),
                create_time TIMESTAMP,
                repair_order INT,
                INDEX idx_log_id (log_id),
                INDEX idx_product_location (product_version_id, bin_location_detail_id)
            )
            """;
        
        try (Statement stmt = connection.createStatement()) {
            stmt.execute(sql);
        }
    }

    /**
     * 检测断链并插入需要修复的完整链路数据
     */
    private int detectAndInsertBrokenChains(Connection connection) throws SQLException {
        // 1. 先创建断链检测表
        createBrokenChainsTable(connection);

        // 2. 插入断链数据
        insertBrokenChainsData(connection);

        // 3. 创建完整链路表并插入从断链点到InventoryAudit的数据
        createAndInsertFullChainData(connection);

        // 4. 将完整链路数据插入修复表
        return insertFullChainToRepairTable(connection);
    }

    /**
     * 创建断链检测表
     */
    private void createBrokenChainsTable(Connection connection) throws SQLException {
        String sql = """
            DROP TABLE IF EXISTS temp_broken_chains;

            CREATE TABLE temp_broken_chains (
                log_id BIGINT NOT NULL,
                product_version_id BIGINT NOT NULL,
                bin_location_detail_id BIGINT NOT NULL,
                qty_side VARCHAR(10) NOT NULL,
                before_qty INT,
                change_qty INT,
                after_qty INT,
                prev_after_qty INT,
                change_type VARCHAR(255),
                create_time TIMESTAMP,
                chain_order INT,
                INDEX idx_product_location (product_version_id, bin_location_detail_id)
            )
            """;

        try (Statement stmt = connection.createStatement()) {
            stmt.execute(sql);
        }
    }

    /**
     * 插入断链数据
     */
    private void insertBrokenChainsData(Connection connection) throws SQLException {
        String sql = """
            INSERT INTO temp_bin_location_log_repair (
                log_id, product_version_id, bin_location_detail_id, qty_side,
                old_before_qty, old_after_qty, old_change_qty, change_type, create_time, repair_order
            )
            WITH qty_union AS (
                SELECT
                    l.id   AS log_id,
                    l.product_version_id,
                    l.product_id,
                    l.source_bin_location_detail_id AS bin_location_detail_id,
                    l.source_bin_location_id        AS bin_location_id,
                    l.create_time,
                    'source'                        AS qty_side,
                    l.source_before_in_stock_qty    AS before_qty,
                    l.source_change_in_stock_qty    AS change_qty,
                    l.source_after_in_stock_qty     AS after_qty,
                    l.change_type
                FROM   bin_location_log l
                WHERE l.remove_flag = 0
                UNION ALL
                SELECT
                    l.id,
                    l.product_version_id,
                    l.product_id,
                    l.dest_bin_location_detail_id,
                    l.dest_bin_location_id,
                    l.create_time,
                    'dest',
                    l.dest_before_in_stock_qty,
                    l.dest_change_in_stock_qty,
                    l.dest_after_in_stock_qty,
                    l.change_type
                FROM   bin_location_log l
                WHERE  NOT (
                           l.dest_bin_location_detail_id = l.source_bin_location_detail_id
                       AND l.dest_change_in_stock_qty    = l.source_change_in_stock_qty
                       )
                       AND l.remove_flag = 0
            )
            , qty_chain AS (
                SELECT
                    q.*,
                    LAG(q.after_qty) OVER (
                        PARTITION BY q.product_version_id, q.bin_location_detail_id
                        ORDER BY     q.create_time, q.log_id, q.qty_side
                    ) AS prev_after_qty,
                    ROW_NUMBER() OVER (
                        PARTITION BY q.product_version_id, q.bin_location_detail_id
                        ORDER BY     q.create_time, q.log_id, q.qty_side
                    ) AS repair_order
                FROM qty_union q
            )
            SELECT
                qc.log_id,
                qc.product_version_id,
                qc.bin_location_detail_id,
                qc.qty_side,
                qc.before_qty,
                qc.after_qty,
                qc.change_qty,
                qc.change_type,
                qc.create_time,
                qc.repair_order
            FROM   qty_chain qc
            WHERE  qc.prev_after_qty IS NOT NULL
              AND  qc.prev_after_qty <> qc.before_qty
            ORDER BY
                qc.product_version_id,
                qc.bin_location_detail_id,
                qc.create_time,
                qc.log_id,
                qc.qty_side
            """;
        
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            return stmt.executeUpdate();
        }
    }

    /**
     * 计算修复数值
     */
    private void calculateRepairValues(Connection connection) throws SQLException {
        // 1. 首先设置初始的new_before_qty为原始值
        String sql1 = """
            UPDATE temp_bin_location_log_repair t1
            INNER JOIN bin_location_log l ON t1.log_id = l.id
            SET t1.new_before_qty = CASE
                WHEN t1.qty_side = 'source' THEN l.source_before_in_stock_qty
                ELSE l.dest_before_in_stock_qty
            END
            """;

        // 2. 创建临时计算表
        String sql2 = """
            DROP TABLE IF EXISTS temp_repair_calc;
            CREATE TABLE temp_repair_calc AS
            SELECT
                t1.log_id,
                t1.product_version_id,
                t1.bin_location_detail_id,
                t1.qty_side,
                t1.repair_order,
                t1.old_before_qty,
                t1.old_after_qty,
                t1.old_change_qty,
                t1.change_type,
                COALESCE(t2.new_after_qty, t1.old_before_qty) AS calculated_before_qty
            FROM temp_bin_location_log_repair t1
            LEFT JOIN temp_bin_location_log_repair t2 ON (
                t2.product_version_id = t1.product_version_id
                AND t2.bin_location_detail_id = t1.bin_location_detail_id
                AND t2.repair_order = t1.repair_order - 1
            )
            """;

        // 3. 更新new_before_qty
        String sql3 = """
            UPDATE temp_bin_location_log_repair t1
            INNER JOIN temp_repair_calc tc ON t1.log_id = tc.log_id
            SET t1.new_before_qty = tc.calculated_before_qty
            """;

        // 4. 计算new_after_qty和new_change_qty
        String sql4 = """
            UPDATE temp_bin_location_log_repair
            SET
                new_after_qty = CASE
                    WHEN change_type = 'InventoryAudit' THEN new_before_qty
                    ELSE new_before_qty + old_change_qty
                END,
                new_change_qty = CASE
                    WHEN change_type = 'InventoryAudit' THEN 0
                    ELSE old_change_qty
                END
            """;

        try (Statement stmt = connection.createStatement()) {
            stmt.executeUpdate(sql1);
            stmt.execute(sql2);
            stmt.executeUpdate(sql3);
            stmt.executeUpdate(sql4);
        }
    }

    /**
     * 获取修复预览
     */
    private List<Map<String, Object>> getRepairPreview(Connection connection) throws SQLException {
        String sql = """
            SELECT 
                log_id,
                product_version_id,
                bin_location_detail_id,
                qty_side,
                change_type,
                old_before_qty,
                new_before_qty,
                old_after_qty,
                new_after_qty,
                old_change_qty,
                new_change_qty
            FROM temp_bin_location_log_repair
            ORDER BY product_version_id, bin_location_detail_id, repair_order
            LIMIT 100
            """;
        
        List<Map<String, Object>> preview = new ArrayList<>();
        try (PreparedStatement stmt = connection.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                Map<String, Object> record = new HashMap<>();
                record.put("logId", rs.getLong("log_id"));
                record.put("productVersionId", rs.getLong("product_version_id"));
                record.put("binLocationDetailId", rs.getLong("bin_location_detail_id"));
                record.put("qtySide", rs.getString("qty_side"));
                record.put("changeType", rs.getString("change_type"));
                record.put("beforeQtyChange", rs.getInt("old_before_qty") + " -> " + rs.getInt("new_before_qty"));
                record.put("afterQtyChange", rs.getInt("old_after_qty") + " -> " + rs.getInt("new_after_qty"));
                
                int oldChangeQty = rs.getInt("old_change_qty");
                int newChangeQty = rs.getInt("new_change_qty");
                if (oldChangeQty != newChangeQty) {
                    record.put("changeQtyInfo", oldChangeQty + " -> " + newChangeQty);
                } else {
                    record.put("changeQtyInfo", String.valueOf(oldChangeQty));
                }
                
                preview.add(record);
            }
        }
        
        return preview;
    }

    /**
     * 执行实际修复
     */
    private int executeActualRepair(Connection connection) throws SQLException {
        // 修复source侧数据
        String sql1 = """
            UPDATE bin_location_log l
            INNER JOIN temp_bin_location_log_repair t ON l.id = t.log_id AND t.qty_side = 'source'
            SET 
                l.source_before_in_stock_qty = t.new_before_qty,
                l.source_after_in_stock_qty = t.new_after_qty,
                l.source_change_in_stock_qty = t.new_change_qty
            """;
        
        // 修复dest侧数据
        String sql2 = """
            UPDATE bin_location_log l
            INNER JOIN temp_bin_location_log_repair t ON l.id = t.log_id AND t.qty_side = 'dest'
            SET 
                l.dest_before_in_stock_qty = t.new_before_qty,
                l.dest_after_in_stock_qty = t.new_after_qty,
                l.dest_change_in_stock_qty = t.new_change_qty
            """;
        
        int totalUpdated = 0;
        try (Statement stmt = connection.createStatement()) {
            totalUpdated += stmt.executeUpdate(sql1);
            totalUpdated += stmt.executeUpdate(sql2);
        }
        
        return totalUpdated;
    }

    /**
     * 获取修复统计
     */
    private Map<String, Object> getRepairStats(Connection connection) throws SQLException {
        String sql = """
            SELECT 
                COUNT(*) AS total_records,
                COUNT(DISTINCT product_version_id) AS affected_products,
                COUNT(DISTINCT bin_location_detail_id) AS affected_locations,
                COUNT(DISTINCT CASE WHEN qty_side = 'source' THEN log_id END) AS source_records,
                COUNT(DISTINCT CASE WHEN qty_side = 'dest' THEN log_id END) AS dest_records,
                COUNT(DISTINCT CASE WHEN change_type = 'InventoryAudit' THEN log_id END) AS inventory_audit_records
            FROM temp_bin_location_log_repair
            """;
        
        Map<String, Object> stats = new HashMap<>();
        try (PreparedStatement stmt = connection.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            if (rs.next()) {
                stats.put("totalRecords", rs.getInt("total_records"));
                stats.put("affectedProducts", rs.getInt("affected_products"));
                stats.put("affectedLocations", rs.getInt("affected_locations"));
                stats.put("sourceRecords", rs.getInt("source_records"));
                stats.put("destRecords", rs.getInt("dest_records"));
                stats.put("inventoryAuditRecords", rs.getInt("inventory_audit_records"));
            }
        }
        
        return stats;
    }

    /**
     * 删除临时表
     */
    private void dropTempTable(Connection connection) throws SQLException {
        String sql1 = "DROP TABLE IF EXISTS temp_repair_calc";
        String sql2 = "DROP TABLE IF EXISTS temp_bin_location_log_repair";
        try (Statement stmt = connection.createStatement()) {
            stmt.execute(sql1);
            stmt.execute(sql2);
        }
    }
}
