package cn.need.cloud.biz.controller.log;

import cn.need.cloud.biz.service.log.BinLocationLogService;
import cn.need.cloud.biz.util.BinLocationLogRepairUtil;
import cn.need.cloud.biz.util.BinLocationLogChainRepairUtil;
import cn.need.framework.common.core.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * BinLocationLog链路修复控制器
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Slf4j
@RestController
@RequestMapping("/api/bin-location-log/repair")
@Tag(name = "BinLocationLog链路修复", description = "BinLocationLog链路断链检测和修复")
public class BinLocationLogRepairController {

    @Resource
    private BinLocationLogService binLocationLogService;

    @Resource
    private BinLocationLogRepairUtil repairUtil;

    @Resource
    private BinLocationLogChainRepairUtil chainRepairUtil;

    /**
     * 检测BinLocationLog链路断链问题
     *
     * @param productVersionId 产品版本ID，可选
     * @param binLocationDetailId 库位详情ID，可选
     * @return 断链检测结果
     */
    @GetMapping("/detect")
    @Operation(summary = "检测断链", description = "检测BinLocationLog链路断链问题")
    public Result<List<Map<String, Object>>> detectBrokenChains(
            @Parameter(description = "产品版本ID，为空则检测所有产品") 
            @RequestParam(required = false) Long productVersionId,
            @Parameter(description = "库位详情ID，为空则检测所有库位") 
            @RequestParam(required = false) Long binLocationDetailId) {
        
        try {
            List<Map<String, Object>> brokenChains = binLocationLogService.detectBrokenChains(productVersionId, binLocationDetailId);
            return Result.success(brokenChains);
        } catch (Exception e) {
            log.error("检测断链失败", e);
            return Result.error("检测断链失败: " + e.getMessage());
        }
    }

    /**
     * 修复BinLocationLog链路断链问题
     *
     * @param productVersionId 产品版本ID，可选
     * @param binLocationDetailId 库位详情ID，可选
     * @return 修复结果
     */
    @PostMapping("/repair")
    @Operation(summary = "修复断链", description = "修复BinLocationLog链路断链问题")
    public Result<Map<String, Object>> repairBrokenChains(
            @Parameter(description = "产品版本ID，为空则修复所有产品") 
            @RequestParam(required = false) Long productVersionId,
            @Parameter(description = "库位详情ID，为空则修复所有库位") 
            @RequestParam(required = false) Long binLocationDetailId) {
        
        try {
            Map<String, Object> result = binLocationLogService.repairBinLocationLogChain(productVersionId, binLocationDetailId);
            return Result.success(result);
        } catch (Exception e) {
            log.error("修复断链失败", e);
            return Result.error("修复断链失败: " + e.getMessage());
        }
    }

    /**
     * 检测并修复特定产品和库位的断链问题
     *
     * @param productVersionId 产品版本ID，必填
     * @param binLocationDetailId 库位详情ID，必填
     * @return 修复结果
     */
    @PostMapping("/repair-specific")
    @Operation(summary = "修复特定断链", description = "修复特定产品和库位的断链问题")
    public Result<Map<String, Object>> repairSpecificChain(
            @Parameter(description = "产品版本ID", required = true) 
            @RequestParam Long productVersionId,
            @Parameter(description = "库位详情ID", required = true) 
            @RequestParam Long binLocationDetailId) {
        
        try {
            Map<String, Object> result = binLocationLogService.repairBinLocationLogChain(productVersionId, binLocationDetailId);
            return Result.success(result);
        } catch (Exception e) {
            log.error("修复特定断链失败", e);
            return Result.error("修复特定断链失败: " + e.getMessage());
        }
    }

    /**
     * 获取修复统计信息
     *
     * @return 统计信息
     */
    @GetMapping("/stats")
    @Operation(summary = "获取修复统计", description = "获取断链检测和修复的统计信息")
    public Result<Map<String, Object>> getRepairStats() {
        
        try {
            // 检测所有断链
            List<Map<String, Object>> allBrokenChains = binLocationLogService.detectBrokenChains(null, null);
            
            // 按产品版本分组统计
            Map<Long, Long> productStats = allBrokenChains.stream()
                .collect(java.util.stream.Collectors.groupingBy(
                    record -> (Long) record.get("product_version_id"),
                    java.util.stream.Collectors.counting()
                ));
            
            // 按库位详情分组统计
            Map<Long, Long> locationStats = allBrokenChains.stream()
                .collect(java.util.stream.Collectors.groupingBy(
                    record -> (Long) record.get("bin_location_detail_id"),
                    java.util.stream.Collectors.counting()
                ));
            
            Map<String, Object> stats = new java.util.HashMap<>();
            stats.put("totalBrokenChains", allBrokenChains.size());
            stats.put("affectedProducts", productStats.size());
            stats.put("affectedLocations", locationStats.size());
            stats.put("productStats", productStats);
            stats.put("locationStats", locationStats);
            
            return Result.success(stats);
        } catch (Exception e) {
            log.error("获取修复统计失败", e);
            return Result.error("获取修复统计失败: " + e.getMessage());
        }
    }

    /**
     * 执行完整的修复流程（使用工具类）
     *
     * @param dryRun 是否为试运行
     * @return 修复结果
     */
    @PostMapping("/repair-full")
    @Operation(summary = "完整修复流程", description = "使用工具类执行完整的修复流程")
    public Result<Map<String, Object>> repairFull(
            @Parameter(description = "是否为试运行，true=不执行实际修复")
            @RequestParam(defaultValue = "true") boolean dryRun) {

        try {
            Map<String, Object> result = repairUtil.executeFullRepair(dryRun);
            return Result.success(result);
        } catch (Exception e) {
            log.error("完整修复流程失败", e);
            return Result.error("完整修复流程失败: " + e.getMessage());
        }
    }
}
