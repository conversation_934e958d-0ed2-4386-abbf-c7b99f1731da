<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.log.BinLocationLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.log.BinLocationLog">
        <result column="id" property="id" />
        <result column="version" property="version" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remove_flag" property="removeFlag" />
        <result column="ref_table_id" property="refTableId" />
        <result column="ref_table_name" property="refTableName" />
        <result column="ref_table_ref_num" property="refTableRefNum" />
        <result column="ref_table_show_name" property="refTableShowName" />
        <result column="ref_table_show_ref_num" property="refTableShowRefNum" />
        <result column="source_bin_location_id" property="sourceBinLocationId" />
        <result column="source_bin_location_detail_id" property="sourceBinLocationDetailId" />
        <result column="source_change_in_stock_qty" property="sourceChangeInStockQty" />
        <result column="source_before_in_stock_qty" property="sourceBeforeInStockQty" />
        <result column="source_after_in_stock_qty" property="sourceAfterInStockQty" />
        <result column="dest_bin_location_id" property="destBinLocationId" />
        <result column="dest_bin_location_detail_id" property="destBinLocationDetailId" />
        <result column="dest_change_in_stock_qty" property="destChangeInStockQty" />
        <result column="dest_before_in_stock_qty" property="destBeforeInStockQty" />
        <result column="dest_after_in_stock_qty" property="destAfterInStockQty" />
        <result column="note" property="note" />
        <result column="deleted_note" property="deletedNote" />
        <result column="tenant_id" property="tenantId" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="product_id" property="productId" />
        <result column="product_version_id" property="productVersionId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id,
        t.version,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.remove_flag,
        t.ref_table_id,
        t.ref_table_name,
        t.ref_table_ref_num,
        t.ref_table_show_name,
        t.ref_table_show_ref_num,
        t.source_bin_location_id,
        t.source_bin_location_detail_id,
        t.source_change_in_stock_qty,
        t.source_before_in_stock_qty,
        t.source_after_in_stock_qty,
        t.dest_bin_location_id,
        t.dest_bin_location_detail_id,
        t.dest_change_in_stock_qty,
        t.dest_before_in_stock_qty,
        t.dest_after_in_stock_qty,
        t.note,
        t.deleted_note,
        t.tenant_id,
        t.warehouse_id,
        t.product_id,
        t.product_version_id,
        t.change_type
    </sql>

    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.log.BinLocationLogPageVO">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            bin_location_log t
        WHERE
            t.remove_flag = 0
            <include refid="Base_Where_List" />
            <include refid="Base_Order_By_List" />
    </select>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <choose>
            <when test="page != null and page.orders != null and page.orders.size() > 0">

            </when>
            <otherwise>
                ORDER BY t.create_time DESC,t.id DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="qo.createTimeStart != null">
            AND t.create_time  &gt;= #{qo.createTimeStart}
        </if>
        <if test="qo.createTimeEnd != null">
            AND t.create_time  &lt; #{qo.createTimeEnd}
        </if>
        <if test="qo.updateTimeStart != null">
            AND t.update_time  &gt;= #{qo.updateTimeStart}
        </if>
        <if test="qo.updateTimeEnd != null">
            AND t.update_time  &lt; #{qo.updateTimeEnd}
        </if>
        <if test="qo.createBy != null">
            AND t.create_by = #{qo.createBy}
        </if>
        <if test="qo.createByList != null and qo.createByList.size > 0 ">
            AND t.create_by in
            <foreach collection="qo.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.updateBy != null">
            AND t.update_by = #{qo.updateBy}
        </if>
        <if test="qo.updateByList != null and qo.updateByList.size > 0 ">
            AND t.update_by in
            <foreach collection="qo.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.refTableId != null">
            AND t.ref_table_id = #{qo.refTableId}
        </if>
        <if test="qo.refTableName != null and qo.refTableName != ''">
            AND t.ref_table_name = #{qo.refTableName}
        </if>
        <if test="qo.refTableRefNum != null and qo.refTableRefNum != ''">
            AND t.ref_table_ref_num = #{qo.refTableRefNum}
        </if>
        <if test="qo.refTableRefNumList != null and qo.refTableRefNumList.size > 0 ">
            AND t.ref_table_ref_num in
            <foreach collection="qo.refTableRefNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.refTableShowNameList != null and qo.refTableShowNameList.size > 0 ">
            AND t.ref_table_show_name in
            <foreach collection="qo.refTableShowNameList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.refTableShowName != null and qo.refTableShowName != ''">
            AND t.ref_table_show_name = #{qo.refTableShowName}
        </if>
        <if test="qo.refTableShowRefNum != null and qo.refTableShowRefNum != ''">
            AND t.ref_table_show_ref_num = #{qo.refTableShowRefNum}
        </if>
        <if test="qo.refTableShowRefNumList != null and qo.refTableShowRefNumList.size > 0 ">
            AND t.ref_table_show_ref_num in
            <foreach collection="qo.refTableShowRefNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.sourceBinLocationId != null">
            AND t.source_bin_location_id = #{qo.sourceBinLocationId}
        </if>
        <if test="qo.destBinLocationId != null">
            AND t.dest_bin_location_id = #{qo.destBinLocationId}
        </if>
        <if test="qo.binLocationId != null">
            AND (t.source_bin_location_id = #{qo.binLocationId} OR t.dest_bin_location_id = #{qo.binLocationId})
        </if>
        <if test="qo.sourceBinLocationDetailId != null">
            AND t.source_bin_location_detail_id = #{qo.sourceBinLocationDetailId}
        </if>
        <if test="qo.sourceChangeInStockQty != null">
            AND t.source_change_in_stock_qty = #{qo.sourceChangeInStockQty}
        </if>
        <if test="qo.sourceBeforeInStockQty != null">
            AND t.source_before_in_stock_qty = #{qo.sourceBeforeInStockQty}
        </if>
        <if test="qo.sourceAfterInStockQty != null">
            AND t.source_after_in_stock_qty = #{qo.sourceAfterInStockQty}
        </if>
        <if test="qo.destBinLocationDetailId != null">
            AND t.dest_bin_location_detail_id = #{qo.destBinLocationDetailId}
        </if>
        <if test="qo.destChangeInStockQty != null">
            AND t.dest_change_in_stock_qty = #{qo.destChangeInStockQty}
        </if>
        <if test="qo.destBeforeInStockQty != null">
            AND t.dest_before_in_stock_qty = #{qo.destBeforeInStockQty}
        </if>
        <if test="qo.destAfterInStockQty != null">
            AND t.dest_after_in_stock_qty = #{qo.destAfterInStockQty}
        </if>
        <if test="qo.note != null and qo.note != ''">
            AND t.note = #{qo.note}
        </if>
        <if test="qo.deletedNote != null and qo.deletedNote != ''">
            AND t.deleted_note = #{qo.deletedNote}
        </if>
        <if test="qo.tenantId != null">
            AND t.tenant_id = #{qo.tenantId}
        </if>
        <if test="qo.warehouseId != null">
            AND t.warehouse_id = #{qo.warehouseId}
        </if>
        <if test="qo.productId != null">
            AND t.product_id = #{qo.productId}
        </if>
        <if test="qo.productVersionId != null">
            AND t.product_version_id = #{qo.productVersionId}
        </if>
        <if test="qo.changeType != null and qo.changeType != ''">
            AND t.change_type = #{qo.changeType}
        </if>
        <if test="qo.changeTypeList != null and qo.changeTypeList.size > 0 ">
            AND t.change_type in
            <foreach collection="qo.changeTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </sql>

    <!-- 检测断链的记录 -->
    <select id="detectBrokenChains" resultType="java.util.Map">
        WITH qty_union AS (
            /* ---------- source 侧 ---------- */
            SELECT
                l.id   AS log_id,
                l.product_version_id,
                l.product_id,
                l.source_bin_location_detail_id AS bin_location_detail_id,
                l.source_bin_location_id        AS bin_location_id,
                l.create_time,
                'source'                        AS qty_side,
                l.source_before_in_stock_qty    AS before_qty,
                l.source_change_in_stock_qty    AS change_qty,
                l.source_after_in_stock_qty     AS after_qty,
                l.change_type
            FROM   bin_location_log l
            WHERE l.remove_flag = 0
            <if test="productVersionId != null">
                AND l.product_version_id = #{productVersionId}
            </if>
            <if test="binLocationDetailId != null">
                AND l.source_bin_location_detail_id = #{binLocationDetailId}
            </if>
            UNION ALL
            /* ---------- dest 侧（若与 source 完全重复则排除） ---------- */
            SELECT
                l.id,
                l.product_version_id,
                l.product_id,
                l.dest_bin_location_detail_id,
                l.dest_bin_location_id,
                l.create_time,
                'dest',
                l.dest_before_in_stock_qty,
                l.dest_change_in_stock_qty,
                l.dest_after_in_stock_qty,
                l.change_type
            FROM   bin_location_log l
            WHERE  NOT (
                       l.dest_bin_location_detail_id = l.source_bin_location_detail_id
                   AND l.dest_change_in_stock_qty    = l.source_change_in_stock_qty
                   )
                   AND l.remove_flag = 0
            <if test="productVersionId != null">
                AND l.product_version_id = #{productVersionId}
            </if>
            <if test="binLocationDetailId != null">
                AND l.dest_bin_location_detail_id = #{binLocationDetailId}
            </if>
        )
        /* ===== 2. 用窗口函数做链路校验 ===== */
        , qty_chain AS (
            SELECT
                q.*,
                LAG(q.after_qty) OVER (
                    PARTITION BY q.product_version_id, q.bin_location_detail_id
                    ORDER BY     q.create_time, q.log_id, q.qty_side
                ) AS prev_after_qty
            FROM qty_union q
        )
        /* ===== 3. 最终结果（按 create_time desc, id desc 排序） ===== */
        SELECT
            qc.product_version_id,
            qc.bin_location_detail_id,
            qc.log_id,
            qc.qty_side,
            qc.before_qty,
            qc.change_qty,
            qc.after_qty,
            qc.create_time,
            qc.prev_after_qty,
            CASE
                WHEN qc.prev_after_qty IS NULL      THEN NULL
                WHEN qc.prev_after_qty = qc.before_qty THEN 1
                ELSE 0
            END AS is_chain_ok,
            qc.change_type
        FROM   qty_chain            qc
        WHERE  qc.prev_after_qty IS NOT NULL        -- 排除组首行
          AND  qc.prev_after_qty &lt;&gt; qc.before_qty    -- 断档判定
        ORDER BY
            qc.create_time DESC,
            qc.log_id     DESC
    </select>

    <!-- 批量更新BinLocationLog记录 - Source侧 -->
    <update id="batchUpdateSourceQtyFields">
        UPDATE bin_location_log
        SET
            source_before_in_stock_qty =
            <foreach collection="updateList" item="item" separator=" " open="CASE id" close="END">
                WHEN #{item.log_id} THEN #{item.new_before_qty}
            </foreach>,
            source_after_in_stock_qty =
            <foreach collection="updateList" item="item" separator=" " open="CASE id" close="END">
                WHEN #{item.log_id} THEN #{item.new_after_qty}
            </foreach>
            <if test="updateList != null and updateList.size() > 0 and updateList[0].new_change_qty != null">
            , source_change_in_stock_qty =
            <foreach collection="updateList" item="item" separator=" " open="CASE id" close="END">
                WHEN #{item.log_id} THEN #{item.new_change_qty}
            </foreach>
            </if>
        WHERE id IN
        <foreach collection="updateList" item="item" separator="," open="(" close=")">
            #{item.log_id}
        </foreach>
    </update>

    <!-- 批量更新BinLocationLog记录 - Dest侧 -->
    <update id="batchUpdateDestQtyFields">
        UPDATE bin_location_log
        SET
            dest_before_in_stock_qty =
            <foreach collection="updateList" item="item" separator=" " open="CASE id" close="END">
                WHEN #{item.log_id} THEN #{item.new_before_qty}
            </foreach>,
            dest_after_in_stock_qty =
            <foreach collection="updateList" item="item" separator=" " open="CASE id" close="END">
                WHEN #{item.log_id} THEN #{item.new_after_qty}
            </foreach>
            <if test="updateList != null and updateList.size() > 0 and updateList[0].new_change_qty != null">
            , dest_change_in_stock_qty =
            <foreach collection="updateList" item="item" separator=" " open="CASE id" close="END">
                WHEN #{item.log_id} THEN #{item.new_change_qty}
            </foreach>
            </if>
        WHERE id IN
        <foreach collection="updateList" item="item" separator="," open="(" close=")">
            #{item.log_id}
        </foreach>
    </update>

    <!-- 获取完整链路数据 -->
    <select id="getFullChainData" resultType="java.util.Map">
        WITH qty_union AS (
            /* ---------- source 侧 ---------- */
            SELECT
                l.id   AS log_id,
                l.product_version_id,
                l.source_bin_location_detail_id AS bin_location_detail_id,
                l.create_time,
                'source'                        AS qty_side,
                l.source_before_in_stock_qty    AS before_qty,
                l.source_change_in_stock_qty    AS change_qty,
                l.source_after_in_stock_qty     AS after_qty,
                l.change_type
            FROM   bin_location_log l
            WHERE l.remove_flag = 0
              AND l.product_version_id = #{productVersionId}
              AND l.source_bin_location_detail_id = #{binLocationDetailId}
            UNION ALL
            /* ---------- dest 侧（若与 source 完全重复则排除） ---------- */
            SELECT
                l.id,
                l.product_version_id,
                l.dest_bin_location_detail_id,
                l.create_time,
                'dest',
                l.dest_before_in_stock_qty,
                l.dest_change_in_stock_qty,
                l.dest_after_in_stock_qty,
                l.change_type
            FROM   bin_location_log l
            WHERE  NOT (
                       l.dest_bin_location_detail_id = l.source_bin_location_detail_id
                   AND l.dest_change_in_stock_qty    = l.source_change_in_stock_qty
                   )
                   AND l.remove_flag = 0
                   AND l.product_version_id = #{productVersionId}
                   AND l.dest_bin_location_detail_id = #{binLocationDetailId}
        )
        /* ===== 2. 添加链路顺序 ===== */
        , qty_chain AS (
            SELECT
                q.*,
                ROW_NUMBER() OVER (
                    PARTITION BY q.product_version_id, q.bin_location_detail_id
                    ORDER BY     q.create_time, q.log_id, q.qty_side
                ) AS chain_order
            FROM qty_union q
        )
        /* ===== 3. 获取指定范围的链路数据 ===== */
        SELECT
            qc.log_id,
            qc.product_version_id,
            qc.bin_location_detail_id,
            qc.qty_side,
            qc.before_qty,
            qc.change_qty,
            qc.after_qty,
            qc.change_type,
            qc.create_time,
            qc.chain_order
        FROM   qty_chain qc
        WHERE  qc.chain_order >= #{startOrder}
          AND  qc.chain_order &lt;= COALESCE(
              (SELECT MIN(qc2.chain_order)
               FROM qty_chain qc2
               WHERE qc2.product_version_id = qc.product_version_id
                 AND qc2.bin_location_detail_id = qc.bin_location_detail_id
                 AND qc2.chain_order > #{startOrder}
                 AND qc2.change_type = 'InventoryAudit'),
              999999  -- 如果没有InventoryAudit，修复到最后
          )
        ORDER BY qc.chain_order
    </select>

</mapper>