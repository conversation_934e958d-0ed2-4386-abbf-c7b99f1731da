package cn.need.cloud.biz.service;

import cn.need.cloud.biz.service.log.BinLocationLogService;
import cn.need.cloud.biz.util.BinLocationLogRepairUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * BinLocationLog链路修复测试
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("dev")
public class BinLocationLogRepairTest {

    @Resource
    private BinLocationLogService binLocationLogService;

    @Resource
    private BinLocationLogRepairUtil repairUtil;

    /**
     * 测试检测断链功能
     */
    @Test
    public void testDetectBrokenChains() {
        log.info("开始测试检测断链功能...");
        
        try {
            // 检测所有断链
            List<Map<String, Object>> brokenChains = binLocationLogService.detectBrokenChains(null, null);
            
            log.info("检测到断链记录数量: {}", brokenChains.size());
            
            if (!brokenChains.isEmpty()) {
                log.info("前5条断链记录:");
                brokenChains.stream().limit(5).forEach(record -> {
                    log.info("LogId: {}, ProductVersionId: {}, BinLocationDetailId: {}, QtySide: {}, ChangeType: {}",
                        record.get("log_id"),
                        record.get("product_version_id"),
                        record.get("bin_location_detail_id"),
                        record.get("qty_side"),
                        record.get("change_type"));
                });
            }
            
        } catch (Exception e) {
            log.error("检测断链失败", e);
        }
    }

    /**
     * 测试修复功能（试运行）
     */
    @Test
    public void testRepairDryRun() {
        log.info("开始测试修复功能（试运行）...");
        
        try {
            Map<String, Object> result = repairUtil.executeFullRepair(true);
            
            log.info("修复结果: {}", result);
            
            if ((Boolean) result.get("success")) {
                log.info("试运行成功!");
                log.info("发现断链记录: {}", result.get("brokenRecords"));
                log.info("影响的产品数: {}", ((Map<?, ?>) result.get("stats")).get("affectedProducts"));
                log.info("影响的库位数: {}", ((Map<?, ?>) result.get("stats")).get("affectedLocations"));
            } else {
                log.error("试运行失败: {}", result.get("message"));
            }
            
        } catch (Exception e) {
            log.error("修复试运行失败", e);
        }
    }

    /**
     * 测试特定产品和库位的修复
     */
    @Test
    public void testRepairSpecific() {
        log.info("开始测试特定产品和库位的修复...");
        
        // 这里需要根据实际数据设置具体的产品版本ID和库位详情ID
        Long productVersionId = 1L; // 替换为实际的产品版本ID
        Long binLocationDetailId = 1L; // 替换为实际的库位详情ID
        
        try {
            // 先检测特定的断链
            List<Map<String, Object>> brokenChains = binLocationLogService.detectBrokenChains(productVersionId, binLocationDetailId);
            
            log.info("特定产品和库位的断链记录数量: {}", brokenChains.size());
            
            if (!brokenChains.isEmpty()) {
                // 执行修复
                Map<String, Object> result = binLocationLogService.repairBinLocationLogChain(productVersionId, binLocationDetailId);
                
                log.info("修复结果: {}", result);
                
                if ((Boolean) result.get("success")) {
                    log.info("修复成功!");
                    log.info("修复的记录数: {}", result.get("repairedCount"));
                } else {
                    log.error("修复失败: {}", result.get("message"));
                }
            } else {
                log.info("没有发现断链记录");
            }
            
        } catch (Exception e) {
            log.error("特定修复失败", e);
        }
    }

    /**
     * 测试修复统计功能
     */
    @Test
    public void testRepairStats() {
        log.info("开始测试修复统计功能...");
        
        try {
            // 检测所有断链
            List<Map<String, Object>> brokenChains = binLocationLogService.detectBrokenChains(null, null);
            
            // 统计分析
            Map<Long, Long> productStats = brokenChains.stream()
                .collect(java.util.stream.Collectors.groupingBy(
                    record -> (Long) record.get("product_version_id"),
                    java.util.stream.Collectors.counting()
                ));
            
            Map<Long, Long> locationStats = brokenChains.stream()
                .collect(java.util.stream.Collectors.groupingBy(
                    record -> (Long) record.get("bin_location_detail_id"),
                    java.util.stream.Collectors.counting()
                ));
            
            log.info("总断链记录数: {}", brokenChains.size());
            log.info("影响的产品数: {}", productStats.size());
            log.info("影响的库位数: {}", locationStats.size());
            
            // 显示前5个影响最严重的产品
            log.info("影响最严重的前5个产品:");
            productStats.entrySet().stream()
                .sorted(Map.Entry.<Long, Long>comparingByValue().reversed())
                .limit(5)
                .forEach(entry -> log.info("产品版本ID: {}, 断链记录数: {}", entry.getKey(), entry.getValue()));
            
            // 显示前5个影响最严重的库位
            log.info("影响最严重的前5个库位:");
            locationStats.entrySet().stream()
                .sorted(Map.Entry.<Long, Long>comparingByValue().reversed())
                .limit(5)
                .forEach(entry -> log.info("库位详情ID: {}, 断链记录数: {}", entry.getKey(), entry.getValue()));
            
        } catch (Exception e) {
            log.error("统计分析失败", e);
        }
    }
}
