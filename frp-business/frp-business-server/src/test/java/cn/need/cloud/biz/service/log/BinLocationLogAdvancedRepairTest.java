package cn.need.cloud.biz.service.log;

import cn.need.cloud.biz.service.log.BinLocationLogDataFixService.AdvancedChainFixResult;
import cn.need.cloud.biz.service.log.BinLocationLogDataFixService.DataAnalysisResult;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDateTime;

/**
 * BinLocationLog 高级链条修复测试
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class BinLocationLogAdvancedRepairTest {

    @Autowired
    private BinLocationLogDataFixService dataFixService;

    @Test
    public void testAnalyzeDataProblems() {
        LocalDateTime startTime = LocalDateTime.of(2024, 1, 1, 0, 0, 0);
        
        log.info("开始分析数据问题测试");
        DataAnalysisResult result = dataFixService.analyzeDataProblems(startTime);
        
        log.info("分析结果: {}", result);
        
        // 验证结果
        assert result != null;
        assert result.getTotalRecords() >= 0;
        assert result.getBrokenChainCount() >= 0;
        assert result.getMathErrorCount() >= 0;
        
        log.info("数据问题分析测试完成");
    }

    @Test
    public void testFixInventoryAuditData() {
        LocalDateTime startTime = LocalDateTime.of(2024, 1, 1, 0, 0, 0);
        
        log.info("开始InventoryAudit修复测试");
        int fixCount = dataFixService.fixInventoryAuditData(startTime);
        
        log.info("InventoryAudit修复记录数: {}", fixCount);
        
        // 验证结果
        assert fixCount >= 0;
        
        log.info("InventoryAudit修复测试完成");
    }

    @Test
    public void testAdvancedChainRepair() {
        LocalDateTime startTime = LocalDateTime.of(2024, 1, 1, 0, 0, 0);
        
        log.info("开始高级链条修复测试");
        
        // 先分析问题
        DataAnalysisResult beforeAnalysis = dataFixService.analyzeDataProblems(startTime);
        log.info("修复前分析: {}", beforeAnalysis);
        
        // 执行高级修复
        AdvancedChainFixResult repairResult = dataFixService.repairBrokenChainsToInventoryAudit(startTime);
        log.info("修复结果: {}", repairResult);
        
        // 再次分析验证
        DataAnalysisResult afterAnalysis = dataFixService.analyzeDataProblems(startTime);
        log.info("修复后分析: {}", afterAnalysis);
        
        // 验证结果
        assert repairResult != null;
        assert repairResult.isSuccess();
        assert repairResult.getBrokenChainsDetected() >= 0;
        assert repairResult.getRecordsFixed() >= 0;
        assert repairResult.getInventoryAuditRecordsFixed() >= 0;
        
        // 验证修复效果
        if (beforeAnalysis.getBrokenChainCount() > 0) {
            // 如果之前有断链，修复后应该减少
            assert afterAnalysis.getBrokenChainCount() <= beforeAnalysis.getBrokenChainCount();
        }
        
        log.info("高级链条修复测试完成");
    }

    @Test
    public void testFullRepairWorkflow() {
        LocalDateTime startTime = LocalDateTime.of(2024, 1, 1, 0, 0, 0);
        
        log.info("开始完整修复流程测试");
        
        // 步骤1：分析问题
        DataAnalysisResult initialAnalysis = dataFixService.analyzeDataProblems(startTime);
        log.info("初始分析: {}", initialAnalysis);
        
        // 步骤2：修复InventoryAudit
        int auditFixCount = dataFixService.fixInventoryAuditData(startTime);
        log.info("InventoryAudit修复数: {}", auditFixCount);
        
        // 步骤3：高级链条修复
        AdvancedChainFixResult chainRepairResult = dataFixService.repairBrokenChainsToInventoryAudit(startTime);
        log.info("链条修复结果: {}", chainRepairResult);
        
        // 步骤4：最终验证
        DataAnalysisResult finalAnalysis = dataFixService.analyzeDataProblems(startTime);
        log.info("最终分析: {}", finalAnalysis);
        
        // 验证整体效果
        assert chainRepairResult.isSuccess();
        
        // 如果有修复操作，验证效果
        if (auditFixCount > 0 || chainRepairResult.getRecordsFixed() > 0) {
            log.info("修复操作已执行，验证修复效果");
            
            // 数学错误应该减少或为0
            assert finalAnalysis.getMathErrorCount() <= initialAnalysis.getMathErrorCount();
            
            // 断链应该减少
            assert finalAnalysis.getBrokenChainCount() <= initialAnalysis.getBrokenChainCount();
        }
        
        log.info("完整修复流程测试完成");
        log.info("测试总结:");
        log.info("  - 初始断链数: {}", initialAnalysis.getBrokenChainCount());
        log.info("  - 初始数学错误: {}", initialAnalysis.getMathErrorCount());
        log.info("  - InventoryAudit修复: {}", auditFixCount);
        log.info("  - 链条修复记录: {}", chainRepairResult.getRecordsFixed());
        log.info("  - InventoryAudit特殊处理: {}", chainRepairResult.getInventoryAuditRecordsFixed());
        log.info("  - 最终断链数: {}", finalAnalysis.getBrokenChainCount());
        log.info("  - 最终数学错误: {}", finalAnalysis.getMathErrorCount());
    }

    @Test
    public void testRebuildChainContinuity() {
        LocalDateTime startTime = LocalDateTime.of(2024, 1, 1, 0, 0, 0);
        
        log.info("开始链条连续性重建测试");
        int fixCount = dataFixService.rebuildChainContinuity(startTime);
        
        log.info("链条连续性重建记录数: {}", fixCount);
        
        // 验证结果
        assert fixCount >= 0;
        
        log.info("链条连续性重建测试完成");
    }

    @Test
    public void testValidateFixResult() {
        LocalDateTime startTime = LocalDateTime.of(2024, 1, 1, 0, 0, 0);
        
        log.info("开始修复结果验证测试");
        var validationResult = dataFixService.validateFixResult(startTime);
        
        log.info("验证结果: {}", validationResult);
        
        // 验证结果
        assert validationResult != null;
        assert validationResult.getTotalRecords() >= 0;
        
        log.info("修复结果验证测试完成");
    }
}
