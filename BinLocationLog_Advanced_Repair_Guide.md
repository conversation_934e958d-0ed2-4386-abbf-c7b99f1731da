# BinLocationLog 高级链条修复指南

## 概述

本指南提供了修复 `bin_location_log` 表中断链问题的完整解决方案。根据您的需求，我们实现了以下功能：

1. **从断链处开始修复**，一直修复到 `changetype = InventoryAudit` 为止
2. **保持 changeqty 正确**，修复 beforeqty 和 afterqty
3. **同时处理 source 和 dest 侧**的数据
4. **特殊处理 InventoryAudit**：当 beforeqty = afterqty 时，设置 changeQty = 0

## 修复方案

### 方案一：使用 Java 服务（推荐）

#### 1. 高级链条修复 API

```bash
# 执行高级链条修复
POST /binlocationlog/datafix/fix/advanced-chain-repair?startTime=2024-01-01 00:00:00

# 响应示例
{
  "success": true,
  "brokenChainsDetected": 150,
  "recordsFixed": 145,
  "inventoryAuditRecordsFixed": 12
}
```

#### 2. 分步骤修复 API

```bash
# 分步骤执行完整修复流程
POST /binlocationlog/datafix/fix/step-by-step?startTime=2024-01-01 00:00:00

# 响应示例
{
  "success": true,
  "step1Analysis": {
    "totalRecords": 5000,
    "brokenChainCount": 150,
    "mathErrorCount": 25,
    "brokenChainPercentage": 3.0
  },
  "step2InventoryAuditFix": 25,
  "step3AdvancedChainRepair": {
    "success": true,
    "brokenChainsDetected": 150,
    "recordsFixed": 145,
    "inventoryAuditRecordsFixed": 12
  },
  "step4Validation": {
    "brokenChainCount": 0,
    "mathErrorCount": 0,
    "overallValid": true
  }
}
```

#### 3. 其他辅助 API

```bash
# 分析数据问题
GET /binlocationlog/datafix/analyze?startTime=2024-01-01 00:00:00

# 验证修复结果
GET /binlocationlog/datafix/validate?startTime=2024-01-01 00:00:00

# 执行完整修复流程
POST /binlocationlog/datafix/fix/full?startTime=2024-01-01 00:00:00
```

### 方案二：使用 SQL 脚本

#### 1. 执行高级修复脚本

```sql
-- 1. 设置起始时间
SET @start_time = '2024-01-01 00:00:00';

-- 2. 执行修复脚本
SOURCE BinLocationLog_Advanced_Chain_Repair.sql;
```

#### 2. 脚本执行步骤

1. **数据备份**：自动创建备份表
2. **检测断链**：使用您提供的 SQL 逻辑检测断链记录
3. **修复断链**：修复 source 和 dest 侧的 beforeqty 和 afterqty
4. **处理 InventoryAudit**：设置 changeQty = 0（当 beforeqty = afterqty 时）
5. **验证结果**：确认修复效果

## 修复逻辑详解

### 1. 断链检测

使用类似您提供的 SQL 逻辑：

```sql
WITH qty_union AS (
    -- 将 source 和 dest 拆成独立行
    SELECT log_id, product_version_id, bin_location_detail_id, 
           qty_side, before_qty, change_qty, after_qty, change_type, create_time
    FROM (source_records UNION ALL dest_records)
)
, qty_chain AS (
    -- 使用窗口函数检测链条连续性
    SELECT *, 
           LAG(after_qty) OVER (
               PARTITION BY product_version_id, bin_location_detail_id
               ORDER BY create_time, log_id, qty_side
           ) AS prev_after_qty
    FROM qty_union
)
SELECT * FROM qty_chain 
WHERE prev_after_qty IS NOT NULL 
  AND prev_after_qty <> before_qty;  -- 断链判定
```

### 2. 修复策略

#### Source 侧修复：
```sql
UPDATE bin_location_log 
SET source_before_in_stock_qty = expected_before_qty,
    source_after_in_stock_qty = expected_before_qty + source_change_in_stock_qty
WHERE 断链条件;
```

#### Dest 侧修复：
```sql
UPDATE bin_location_log 
SET dest_before_in_stock_qty = expected_before_qty,
    dest_after_in_stock_qty = expected_before_qty + dest_change_in_stock_qty
WHERE 断链条件;
```

#### InventoryAudit 特殊处理：
```sql
UPDATE bin_location_log 
SET source_change_in_stock_qty = 0
WHERE change_type = 'InventoryAudit'
  AND source_before_in_stock_qty = source_after_in_stock_qty
  AND source_change_in_stock_qty != 0;
```

### 3. 修复原则

1. **保持 changeqty 不变**：因为 changeqty 是正确的
2. **修复 beforeqty**：使其等于前一条记录的 afterqty
3. **重新计算 afterqty**：afterqty = beforeqty + changeqty
4. **处理 InventoryAudit**：当盘点结果显示库存无变化时，设置 changeQty = 0

## 验证标准

修复成功的标准：

1. ✅ **链条连续性**：断链记录数 = 0
2. ✅ **数学关系**：所有记录满足 `beforeqty + changeqty = afterqty`
3. ✅ **InventoryAudit 一致性**：当 beforeqty = afterqty 时，changeQty = 0

## 使用建议

### 生产环境使用

1. **选择低峰期执行**
2. **分批处理大量数据**
3. **先在测试环境验证**
4. **准备回滚方案**

### 推荐执行顺序

1. 🔍 **分析问题**：`GET /analyze`
2. 🔧 **执行修复**：`POST /fix/advanced-chain-repair`
3. ✅ **验证结果**：`GET /validate`
4. 📊 **检查报告**：确认修复效果

### 监控指标

- 断链记录数量
- 数学错误记录数量
- InventoryAudit 记录修复数量
- 修复耗时

## 故障排除

### 常见问题

1. **修复后仍有断链**
   - 检查第一条记录的 beforeqty 是否正确
   - 确认时间范围设置是否合适

2. **InventoryAudit 修复异常**
   - 检查 afterqty 和 changeqty 的原始数据
   - 确认业务逻辑是否正确

3. **性能问题**
   - 分批处理大量数据
   - 在低峰期执行
   - 考虑添加临时索引

### 回滚方案

如果修复出现问题，可以从备份表恢复：

```sql
-- 恢复数据（谨慎操作）
UPDATE bin_location_log bl
JOIN bin_location_log_backup_advanced backup ON bl.id = backup.id
SET bl.source_before_in_stock_qty = backup.source_before_in_stock_qty,
    bl.source_after_in_stock_qty = backup.source_after_in_stock_qty,
    bl.source_change_in_stock_qty = backup.source_change_in_stock_qty,
    bl.dest_before_in_stock_qty = backup.dest_before_in_stock_qty,
    bl.dest_after_in_stock_qty = backup.dest_after_in_stock_qty,
    bl.dest_change_in_stock_qty = backup.dest_change_in_stock_qty
WHERE bl.create_time >= '2024-01-01 00:00:00';
```

## 总结

这个高级修复方案完全按照您的需求设计：

- ✅ 从断链处开始修复到 InventoryAudit
- ✅ 保持 changeqty 正确，修复 beforeqty 和 afterqty  
- ✅ 同时处理 source 和 dest 侧
- ✅ InventoryAudit 特殊处理（beforeqty = afterqty 时设置 changeQty = 0）
- ✅ 提供 Java API 和 SQL 脚本两种方式
- ✅ 完整的验证和监控机制

选择适合您环境的方案执行即可！
