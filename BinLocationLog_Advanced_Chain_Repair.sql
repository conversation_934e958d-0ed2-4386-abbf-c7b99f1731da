-- =====================================================
-- BinLocationLog 高级链条修复 SQL 脚本
-- =====================================================
-- 
-- 修复目标：
-- 1. 从断链的地方修复，一直修复到 changetype = InventoryAudit 为止
-- 2. 修复的时候，changeqty 是对的，所以是修复 beforeqty, afterqty
-- 3. source 和 dest 涉及到的都需要修复
-- 4. 修复到 changetype = InventoryAudit 的时候，beforeqty 应该要等于 afterqty，这个时候需将 changeQty 设置为 0
--
-- 使用方法：
-- 1. 设置起始时间变量
-- 2. 执行备份
-- 3. 执行修复脚本
-- 4. 验证结果
-- =====================================================

-- 设置修复的起始时间（请根据实际需要修改）
SET @start_time = '2024-01-01 00:00:00';

-- =====================================================
-- 第一步：数据备份
-- =====================================================

-- 创建备份表
CREATE TABLE IF NOT EXISTS bin_location_log_backup_advanced AS 
SELECT * FROM frp_log.bin_location_log 
WHERE create_time >= @start_time AND remove_flag = 0;

-- 验证备份
SELECT 
    COUNT(*) as backup_count,
    MIN(create_time) as min_time,
    MAX(create_time) as max_time
FROM bin_location_log_backup_advanced;

-- =====================================================
-- 第二步：检测断链记录
-- =====================================================

-- 创建临时表存储断链记录
DROP TEMPORARY TABLE IF EXISTS broken_chains;
CREATE TEMPORARY TABLE broken_chains AS
WITH qty_union AS (
    /* ---------- source 侧 ---------- */
    SELECT
        l.id   AS log_id,
        l.product_version_id,
        l.product_id,
        l.source_bin_location_detail_id AS bin_location_detail_id,
        l.source_bin_location_id        AS bin_location_id,
        l.create_time,
        'source'                        AS qty_side,
        l.source_before_in_stock_qty    AS before_qty,
        l.source_change_in_stock_qty    AS change_qty,
        l.source_after_in_stock_qty     AS after_qty,
        l.change_type
    FROM   frp_log.bin_location_log l
    WHERE l.remove_flag = 0 AND l.create_time >= @start_time
    UNION ALL
    /* ---------- dest 侧（若与 source 完全重复则排除） ---------- */
    SELECT
        l.id,
        l.product_version_id,
        l.product_id,
        l.dest_bin_location_detail_id,
        l.dest_bin_location_id,
        l.create_time,
        'dest',
        l.dest_before_in_stock_qty,
        l.dest_change_in_stock_qty,
        l.dest_after_in_stock_qty,
        l.change_type
    FROM   frp_log.bin_location_log l
    WHERE  NOT (
               l.dest_bin_location_detail_id = l.source_bin_location_detail_id
           AND l.dest_change_in_stock_qty    = l.source_change_in_stock_qty
           )
           AND l.remove_flag = 0 AND l.create_time >= @start_time
)
/* ===== 用窗口函数做链路校验 ===== */
, qty_chain AS (
    SELECT
        q.*,
        LAG(q.after_qty) OVER (
            PARTITION BY q.product_version_id, q.bin_location_detail_id
            ORDER BY     q.create_time, q.log_id, q.qty_side
        ) AS prev_after_qty
    FROM qty_union q
)
/* ===== 检测断链记录 ===== */
SELECT
    qc.log_id,
    qc.product_version_id,
    qc.bin_location_detail_id,
    qc.qty_side,
    qc.before_qty AS current_before_qty,
    qc.prev_after_qty AS expected_before_qty,
    qc.change_type,
    qc.create_time
FROM   qty_chain qc
WHERE  qc.prev_after_qty IS NOT NULL        -- 排除组首行
  AND  qc.prev_after_qty <> qc.before_qty;  -- 断档判定

-- 显示检测到的断链记录数
SELECT 
    COUNT(*) as broken_chain_count,
    COUNT(DISTINCT CONCAT(product_version_id, '_', bin_location_detail_id)) as affected_groups
FROM broken_chains;

-- =====================================================
-- 第三步：修复断链记录
-- =====================================================

-- 修复source侧的断链记录
UPDATE frp_log.bin_location_log bl
JOIN broken_chains bc ON bl.id = bc.log_id AND bc.qty_side = 'source'
SET 
    bl.source_before_in_stock_qty = bc.expected_before_qty,
    bl.source_after_in_stock_qty = bc.expected_before_qty + bl.source_change_in_stock_qty,
    bl.update_time = NOW(),
    bl.update_by = 1
WHERE bl.create_time >= @start_time;

-- 修复dest侧的断链记录
UPDATE frp_log.bin_location_log bl
JOIN broken_chains bc ON bl.id = bc.log_id AND bc.qty_side = 'dest'
SET 
    bl.dest_before_in_stock_qty = bc.expected_before_qty,
    bl.dest_after_in_stock_qty = bc.expected_before_qty + bl.dest_change_in_stock_qty,
    bl.update_time = NOW(),
    bl.update_by = 1
WHERE bl.create_time >= @start_time;

-- 显示修复统计
SELECT 
    'source修复' as fix_type,
    COUNT(*) as records_fixed
FROM frp_log.bin_location_log bl
JOIN broken_chains bc ON bl.id = bc.log_id AND bc.qty_side = 'source'
WHERE bl.update_time >= CURDATE()

UNION ALL

SELECT 
    'dest修复' as fix_type,
    COUNT(*) as records_fixed
FROM frp_log.bin_location_log bl
JOIN broken_chains bc ON bl.id = bc.log_id AND bc.qty_side = 'dest'
WHERE bl.update_time >= CURDATE();

-- =====================================================
-- 第四步：修复InventoryAudit记录的changeQty
-- =====================================================

-- 对于InventoryAudit类型，如果beforeqty等于afterqty，设置changeQty为0
UPDATE frp_log.bin_location_log 
SET 
    source_change_in_stock_qty = 0,
    update_time = NOW(),
    update_by = 1
WHERE change_type = 'InventoryAudit'
  AND create_time >= @start_time
  AND source_before_in_stock_qty = source_after_in_stock_qty
  AND source_change_in_stock_qty != 0;

UPDATE frp_log.bin_location_log 
SET 
    dest_change_in_stock_qty = 0,
    update_time = NOW(),
    update_by = 1
WHERE change_type = 'InventoryAudit'
  AND create_time >= @start_time
  AND dest_before_in_stock_qty = dest_after_in_stock_qty
  AND dest_change_in_stock_qty != 0;

-- 显示InventoryAudit修复统计
SELECT 
    'InventoryAudit source修复' as fix_type,
    COUNT(*) as records_fixed
FROM frp_log.bin_location_log 
WHERE change_type = 'InventoryAudit'
  AND create_time >= @start_time
  AND source_change_in_stock_qty = 0
  AND update_time >= CURDATE()

UNION ALL

SELECT 
    'InventoryAudit dest修复' as fix_type,
    COUNT(*) as records_fixed
FROM frp_log.bin_location_log 
WHERE change_type = 'InventoryAudit'
  AND create_time >= @start_time
  AND dest_change_in_stock_qty = 0
  AND update_time >= CURDATE();

-- =====================================================
-- 第五步：验证修复结果
-- =====================================================

-- 重新检测断链（应该为0）
WITH qty_union AS (
    SELECT
        l.id   AS log_id,
        l.product_version_id,
        l.source_bin_location_detail_id AS bin_location_detail_id,
        l.create_time,
        'source' AS qty_side,
        l.source_before_in_stock_qty    AS before_qty,
        l.source_change_in_stock_qty    AS change_qty,
        l.source_after_in_stock_qty     AS after_qty,
        l.change_type
    FROM   frp_log.bin_location_log l
    WHERE l.remove_flag = 0 AND l.create_time >= @start_time
    UNION ALL
    SELECT
        l.id,
        l.product_version_id,
        l.dest_bin_location_detail_id,
        l.create_time,
        'dest',
        l.dest_before_in_stock_qty,
        l.dest_change_in_stock_qty,
        l.dest_after_in_stock_qty,
        l.change_type
    FROM   frp_log.bin_location_log l
    WHERE  NOT (
               l.dest_bin_location_detail_id = l.source_bin_location_detail_id
           AND l.dest_change_in_stock_qty    = l.source_change_in_stock_qty
           )
           AND l.remove_flag = 0 AND l.create_time >= @start_time
)
, qty_chain AS (
    SELECT
        q.*,
        LAG(q.after_qty) OVER (
            PARTITION BY q.product_version_id, q.bin_location_detail_id
            ORDER BY     q.create_time, q.log_id, q.qty_side
        ) AS prev_after_qty
    FROM qty_union q
)
SELECT
    COUNT(*) as remaining_broken_chains,
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ 修复成功：无断链记录'
        ELSE CONCAT('❌ 仍有断链：', COUNT(*), '条记录')
    END as repair_status
FROM   qty_chain qc
WHERE  qc.prev_after_qty IS NOT NULL
  AND  qc.prev_after_qty <> qc.before_qty;

-- 验证数学关系（应该为0）
SELECT 
    COUNT(*) as math_error_count,
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ 数学关系正确'
        ELSE CONCAT('❌ 数学错误：', COUNT(*), '条记录')
    END as math_status
FROM frp_log.bin_location_log
WHERE create_time >= @start_time
  AND remove_flag = 0
  AND (
      (source_before_in_stock_qty + source_change_in_stock_qty != source_after_in_stock_qty)
      OR 
      (dest_before_in_stock_qty IS NOT NULL AND dest_change_in_stock_qty IS NOT NULL 
       AND dest_before_in_stock_qty + dest_change_in_stock_qty != dest_after_in_stock_qty)
  );

-- 清理临时表
DROP TEMPORARY TABLE IF EXISTS broken_chains;

-- =====================================================
-- 修复完成提示
-- =====================================================
SELECT 
    '🎉 BinLocationLog 高级链条修复完成！' as message,
    NOW() as completion_time,
    @start_time as repair_start_time;
