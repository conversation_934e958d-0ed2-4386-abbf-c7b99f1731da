# BinLocationLog完整链路修复指南

## 概述

本文档介绍了BinLocationLog完整链路修复功能的使用方法。该功能解决了原有修复逻辑的关键缺陷：**从断链点修复到下一个InventoryAudit操作**，而不是仅修复检测到的断链记录。

## 问题背景

### 原有问题
- 原修复逻辑只修复检测到的断链记录
- 当链路断开后，后续所有记录的before_qty都是错误的
- 需要修复从断链点到下一个InventoryAudit操作的完整链路

### 解决方案
- 检测断链点
- 获取从断链点到下一个InventoryAudit操作的完整链路
- 按顺序修复整个链路的数量值
- 对InventoryAudit操作特殊处理：before_qty = after_qty, change_qty = 0

## 新增功能

### 1. 完整链路修复工具类
`BinLocationLogChainRepairUtil.java`
- 实现从断链点到InventoryAudit的完整链路修复
- 支持试运行模式和实际修复模式
- 提供详细的修复统计信息

### 2. 新增API接口

#### 2.1 完整链路修复
```
POST /api/bin-location-log/repair/repair-full-chain?dryRun=true
```

**参数：**
- `dryRun`: 是否为试运行模式（默认true）
  - `true`: 仅预览修复数据，不执行实际修复
  - `false`: 执行实际修复

**响应示例：**
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "success": true,
    "message": "修复完成",
    "broken_chains_count": 15,
    "full_chain_count": 45,
    "repair_data_count": 45,
    "repaired_count": 0,
    "dry_run": true,
    "repair_preview": [
      {
        "log_id": 12345,
        "qty_side": "source",
        "old_before_qty": 100,
        "old_after_qty": 90,
        "old_change_qty": -10,
        "new_before_qty": 95,
        "new_after_qty": 85,
        "new_change_qty": -10,
        "change_type": "Pick"
      }
    ]
  }
}
```

#### 2.2 修复统计信息
```
GET /api/bin-location-log/repair/repair-stats
```

**响应示例：**
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "broken_chains_count": 15,
    "full_chain_count": 45,
    "repair_data_count": 45,
    "success": true,
    "message": "统计信息获取成功"
  }
}
```

## 使用流程

### 1. 获取修复统计
首先调用统计接口了解需要修复的数据量：
```bash
curl -X GET "http://localhost:8080/api/bin-location-log/repair/repair-stats"
```

### 2. 试运行预览
执行试运行查看修复预览：
```bash
curl -X POST "http://localhost:8080/api/bin-location-log/repair/repair-full-chain?dryRun=true"
```

### 3. 执行实际修复
确认无误后执行实际修复：
```bash
curl -X POST "http://localhost:8080/api/bin-location-log/repair/repair-full-chain?dryRun=false"
```

## 修复逻辑说明

### 1. 断链检测
使用窗口函数检测prev_after_qty ≠ before_qty的记录

### 2. 链路范围确定
- 从断链点开始
- 到下一个InventoryAudit操作结束
- 如果没有InventoryAudit，修复到链路末尾

### 3. 修复计算
- 获取断链点的正确起始值（prev_after_qty）
- 按chain_order顺序修复每个记录
- 普通操作：new_after_qty = new_before_qty + change_qty
- InventoryAudit操作：new_before_qty = new_after_qty, new_change_qty = 0

### 4. 批量更新
- 分别更新source侧和dest侧数据
- 使用批量更新提高性能
- 支持事务回滚

## 与原有功能对比

| 功能 | 原有修复 | 完整链路修复 |
|------|----------|--------------|
| 修复范围 | 仅断链记录 | 断链点到InventoryAudit |
| 修复准确性 | 部分修复 | 完整修复 |
| InventoryAudit处理 | 普通处理 | 特殊处理 |
| 试运行支持 | 支持 | 支持 |
| 性能 | 较快 | 中等 |

## 注意事项

1. **备份数据**：执行实际修复前建议备份相关数据
2. **试运行**：务必先执行试运行确认修复数据正确
3. **分批处理**：对于大量数据，建议分批处理
4. **监控性能**：修复过程中注意数据库性能
5. **验证结果**：修复完成后验证链路连续性

## 故障排除

### 1. 修复失败
- 检查数据库连接
- 确认事务隔离级别
- 查看错误日志

### 2. 性能问题
- 检查索引是否存在
- 考虑分批处理
- 监控数据库负载

### 3. 数据不一致
- 重新执行断链检测
- 检查并发修改
- 验证修复逻辑

## 相关文件

- `BinLocationLogChainRepairUtil.java` - 完整链路修复工具类
- `BinLocationLogRepairController.java` - 修复API控制器
- `bin_location_log_chain_repair_simple.sql` - SQL脚本版本
- `BinLocationLogServiceImpl.java` - 服务层实现

## 更新日志

- 2025-01-07: 创建完整链路修复功能
- 2025-01-07: 添加新的API接口
- 2025-01-07: 完善文档和使用指南
