# BinLocationLog链路修复系统使用指南

## 概述

BinLocationLog链路修复系统用于检测和修复BinLocationLog表中的断链问题。断链是指在库存操作记录中，前一条记录的`after_qty`与后一条记录的`before_qty`不匹配的情况。

## 问题描述

在仓库管理系统中，BinLocationLog记录了所有库存变动的历史。每条记录包含：
- `before_qty`: 操作前库存数量
- `change_qty`: 变动数量
- `after_qty`: 操作后库存数量

正常情况下，同一产品同一库位的连续记录应该满足：
```
记录N的after_qty = 记录N+1的before_qty
```

当这个关系被破坏时，就形成了"断链"。

## 修复原理

修复系统的核心逻辑：
1. **保持change_qty不变** - 因为change_qty反映了实际的业务操作
2. **修复before_qty和after_qty** - 基于正确的链路关系重新计算
3. **特殊处理InventoryAudit操作** - 盘点操作时设置before_qty = after_qty，change_qty = 0

## 使用方式

### 1. API接口方式

#### 检测断链
```http
GET /api/bin-location-log/repair/detect?productVersionId=123&binLocationDetailId=456
```

#### 修复断链
```http
POST /api/bin-location-log/repair/repair?productVersionId=123&binLocationDetailId=456
```

#### 完整修复流程（推荐）
```http
POST /api/bin-location-log/repair/repair-full?dryRun=true
```

参数说明：
- `productVersionId`: 产品版本ID（可选，为空则处理所有产品）
- `binLocationDetailId`: 库位详情ID（可选，为空则处理所有库位）
- `dryRun`: 是否为试运行（true=不执行实际修复，只显示预览）

#### 获取统计信息
```http
GET /api/bin-location-log/repair/stats
```

### 2. SQL脚本方式

提供两个版本的SQL脚本：

#### 标准版本
`sql/repair/bin_location_log_chain_repair.sql` - 完整功能版本

#### 简化版本（推荐）
`sql/repair/bin_location_log_chain_repair_simple.sql` - 避免MySQL子查询限制

该脚本会：
1. 创建临时表存储修复数据
2. 检测所有断链记录
3. 计算修复后的正确数值
4. 显示修复预览
5. 提供修复统计信息

**注意：默认情况下脚本只显示预览，不执行实际修复。要执行修复，需要取消注释第8步中的UPDATE语句。**

### 3. Java测试方式

运行测试类：`BinLocationLogRepairTest`

包含以下测试方法：
- `testDetectBrokenChains()`: 测试检测功能
- `testRepairDryRun()`: 测试修复功能（试运行）
- `testRepairSpecific()`: 测试特定产品和库位的修复
- `testRepairStats()`: 测试统计功能

## 核心组件

### 1. Service层
- `BinLocationLogService`: 服务接口
- `BinLocationLogServiceImpl`: 服务实现

### 2. Mapper层
- `BinLocationLogMapper`: 数据访问接口
- `BinLocationLogMapper.xml`: SQL映射文件

### 3. Controller层
- `BinLocationLogRepairController`: REST API控制器

### 4. 工具类
- `BinLocationLogRepairUtil`: 修复工具类，提供完整的修复流程

## 修复流程详解

### 1. 检测阶段
使用复杂的SQL查询检测断链：
```sql
WITH qty_union AS (
    -- 合并source和dest侧的数据
    SELECT ... FROM bin_location_log ...
    UNION ALL
    SELECT ... FROM bin_location_log ...
)
, qty_chain AS (
    -- 使用窗口函数检测链路
    SELECT *, LAG(after_qty) OVER (...) AS prev_after_qty
    FROM qty_union
)
SELECT * FROM qty_chain 
WHERE prev_after_qty IS NOT NULL 
  AND prev_after_qty <> before_qty
```

### 2. 修复阶段
对于每个断链记录：
1. 计算正确的`before_qty` = 前一条记录的`after_qty`
2. 计算正确的`after_qty` = `before_qty` + `change_qty`
3. 特殊处理：如果是InventoryAudit操作，设置`after_qty` = `before_qty`，`change_qty` = 0

### 3. 更新阶段
分别更新source侧和dest侧的数据：
```sql
UPDATE bin_location_log 
SET source_before_in_stock_qty = ?, 
    source_after_in_stock_qty = ?,
    source_change_in_stock_qty = ?
WHERE id IN (...)
```

## 安全措施

1. **事务保护**: 所有修复操作都在事务中执行，失败时自动回滚
2. **试运行模式**: 默认为试运行模式，不会修改实际数据
3. **详细日志**: 记录所有修复操作的详细信息
4. **分批处理**: 支持按产品或库位分批修复，避免大批量操作
5. **数据验证**: 修复前后都会进行数据一致性检查

## 注意事项

1. **备份数据**: 执行修复前建议备份相关数据
2. **测试环境**: 建议先在测试环境验证修复效果
3. **业务影响**: 修复过程中可能会锁定相关记录，建议在业务低峰期执行
4. **监控日志**: 密切关注修复过程中的日志输出
5. **验证结果**: 修复完成后建议重新检测确认问题已解决

## 常见问题

### Q: 修复后还有断链怎么办？
A: 可能存在数据依赖关系，建议多次运行修复直到没有断链为止。

### Q: InventoryAudit记录为什么特殊处理？
A: 盘点操作是重新确定库存数量，不是基于之前数量的增减，所以before_qty应该等于after_qty。

### Q: 修复会影响业务数据吗？
A: 修复只调整qty字段的数值，不会改变业务逻辑，但建议在业务低峰期执行。

### Q: 如何验证修复效果？
A: 修复完成后重新运行检测功能，确认没有断链记录。

### Q: SQL脚本执行时出现"You can't specify target table for update in FROM clause"错误？
A: 这是MySQL的限制，请使用简化版本的SQL脚本：`bin_location_log_chain_repair_simple.sql`

### Q: 为什么需要两个版本的SQL脚本？
A: 标准版本功能更完整但可能遇到MySQL子查询限制，简化版本通过分步骤执行避免了这个问题。

## 联系方式

如有问题请联系开发团队或查看相关技术文档。
